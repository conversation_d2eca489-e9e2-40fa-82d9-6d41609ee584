{"data_mtime": 1749061352, "dep_lines": [34, 33, 22, 25, 26, 27, 31, 32, 33, 17, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.trocr.configuration_trocr", "transformers.utils.logging", "torch.nn", "transformers.activations", "transformers.generation", "transformers.modeling_attn_mask_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "copy", "math", "typing", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.sparse", "torch.types", "torch.utils", "torch.utils._contextlib", "transformers.configuration_utils", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub"], "hash": "c31ac2aff88a795449e6e82b40dadd902e115b80", "id": "transformers.models.trocr.modeling_trocr", "ignore_all": true, "interface_hash": "38e15c64ef78580a6349f2d93b1fa5ed167a1dc3", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\trocr\\modeling_trocr.py", "plugin_data": null, "size": 40086, "suppressed": [], "version_id": "1.16.0"}