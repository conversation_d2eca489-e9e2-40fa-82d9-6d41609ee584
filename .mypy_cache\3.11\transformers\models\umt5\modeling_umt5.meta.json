{"data_mtime": 1749061352, "dep_lines": [48, 52, 39, 54, 22, 25, 26, 27, 28, 29, 38, 39, 17, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.umt5.configuration_umt5", "torch.nn.attention.flex_attention", "transformers.utils.logging", "transformers.integrations.flex_attention", "torch.nn", "transformers.activations", "transformers.cache_utils", "transformers.generation", "transformers.modeling_attn_mask_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "copy", "math", "typing", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.attention", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.nn.modules.sparse", "torch.nn.parameter", "transformers.configuration_utils", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "000c37bb7dd866b0518e8774efd9c1e7bc4ff7b9", "id": "transformers.models.umt5.modeling_umt5", "ignore_all": true, "interface_hash": "a96b2401490e8c23856ef398229cc0c493921976", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\umt5\\modeling_umt5.py", "plugin_data": null, "size": 93208, "suppressed": [], "version_id": "1.16.0"}