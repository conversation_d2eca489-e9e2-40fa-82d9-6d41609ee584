{"data_mtime": 1749061380, "dep_lines": [1, 5, 9, 16, 19, 22, 27, 30, 31, 34, 37, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["torch.utils.data.datapipes.iter.callable", "torch.utils.data.datapipes.iter.combinatorics", "torch.utils.data.datapipes.iter.combining", "torch.utils.data.datapipes.iter.filelister", "torch.utils.data.datapipes.iter.fileopener", "torch.utils.data.datapipes.iter.grouping", "torch.utils.data.datapipes.iter.routeddecoder", "torch.utils.data.datapipes.iter.selecting", "torch.utils.data.datapipes.iter.sharding", "torch.utils.data.datapipes.iter.streamreader", "torch.utils.data.datapipes.iter.utils", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "cfdf16f53c1c3e65c47211f08bc35b12e6929e1d", "id": "torch.utils.data.datapipes.iter", "ignore_all": true, "interface_hash": "0454dc4594e14ba7b01f6af3c6f3a5e1c674a9b7", "mtime": 1749057391, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\__init__.py", "plugin_data": null, "size": 1880, "suppressed": [], "version_id": "1.16.0"}