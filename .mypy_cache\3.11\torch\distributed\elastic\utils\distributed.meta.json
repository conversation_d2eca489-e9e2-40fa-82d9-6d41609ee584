{"data_mtime": 1749061368, "dep_lines": [16, 17, 15, 9, 10, 11, 12, 13, 15, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["torch.distributed.elastic.utils.logging", "torch.distributed.elastic.utils.store", "torch.distributed", "datetime", "os", "socket", "contextlib", "typing", "torch", "builtins", "_frozen_importlib", "_socket", "abc"], "hash": "9e93b58150d52217b1f0af6bb3c25c4645a753c5", "id": "torch.distributed.elastic.utils.distributed", "ignore_all": true, "interface_hash": "c90f5979286746e0b7b9d7f86571a4b7fedd950a", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\elastic\\utils\\distributed.py", "plugin_data": null, "size": 6107, "suppressed": [], "version_id": "1.16.0"}