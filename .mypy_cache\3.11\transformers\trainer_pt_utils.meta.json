{"data_mtime": 1749061347, "dep_lines": [38, 37, 40, 42, 58, 26, 35, 36, 41, 42, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1174, 55, 1174, 55, 1174], "dep_prios": [5, 5, 5, 10, 5, 5, 10, 10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20, 20, 20], "dependencies": ["torch.utils.data.distributed", "torch.utils.data", "transformers.integrations.deepspeed", "transformers.utils.logging", "torch.optim.lr_scheduler", "collections.abc", "torch.distributed", "torch.nn", "transformers.tokenization_utils_base", "transformers.utils", "copy", "datetime", "io", "json", "math", "os", "sys", "warnings", "contextlib", "dataclasses", "itertools", "logging", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "_typeshed", "_warnings", "abc", "collections", "enum", "functools", "numpy._typing", "numpy._typing._ufunc", "torch._C", "torch._C._VariableFunctions", "torch._C._distributed_c10d", "torch._tensor", "torch.distributed.distributed_c10d", "torch.optim", "torch.optim.optimizer", "torch.types", "torch.utils", "torch.utils.data.dataset", "torch.utils.data.sampler", "transformers.utils.generic", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "63a0491e6e2d22911f14e8809de1e51cf11858db", "id": "transformers.trainer_pt_utils", "ignore_all": true, "interface_hash": "486a025e9f26517f55a914b50a042080a9e15089", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\trainer_pt_utils.py", "plugin_data": null, "size": 61634, "suppressed": ["smdistributed.modelparallel.torch", "torch_xla.runtime", "smdistributed.modelparallel", "torch_xla", "smdistributed"], "version_id": "1.16.0"}