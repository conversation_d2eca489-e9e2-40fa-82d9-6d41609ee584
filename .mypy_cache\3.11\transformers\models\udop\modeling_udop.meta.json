{"data_mtime": 1749061352, "dep_lines": [51, 53, 27, 31, 36, 37, 38, 39, 40, 41, 42, 17, 18, 19, 20, 21, 22, 23, 24, 26, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.attention.flex_attention", "transformers.integrations.flex_attention", "torch.nn", "transformers.modeling_outputs", "transformers.activations", "transformers.cache_utils", "transformers.generation", "transformers.modeling_attn_mask_utils", "transformers.modeling_utils", "transformers.pytorch_utils", "transformers.utils", "collections", "logging", "math", "random", "abc", "copy", "dataclasses", "typing", "torch", "transformers", "builtins", "_frozen_importlib", "collections.abc", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.attention", "torch.nn.functional", "torch.nn.init", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.sparse", "torch.nn.parameter", "transformers.configuration_utils", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.models.udop.configuration_udop", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "192d37aa7a78065d522923d4f96a25d1b35c52b2", "id": "transformers.models.udop.modeling_udop", "ignore_all": true, "interface_hash": "d55646e9a0714e2edb987806d7d34f61522aefce", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\udop\\modeling_udop.py", "plugin_data": null, "size": 95244, "suppressed": [], "version_id": "1.16.0"}