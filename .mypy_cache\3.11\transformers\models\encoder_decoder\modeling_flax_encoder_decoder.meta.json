{"data_mtime": 1749061347, "dep_lines": [31, 32, 33, 30, 28, 29, 30, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23, 20, 22, 24, 26, 20, 21], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10, 5, 5, 20, 5], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.auto.modeling_flax_auto", "transformers.models.encoder_decoder.configuration_encoder_decoder", "transformers.utils.logging", "transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "os", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "collections", "logging", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "a9a23922a2d41ab9e1c6ccfb72e6c120023d080c", "id": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder", "ignore_all": true, "interface_hash": "02f2b9b5109f23c7856a72257d05dd3bba3acb49", "mtime": 1749058945, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\modeling_flax_encoder_decoder.py", "plugin_data": null, "size": 43608, "suppressed": ["flax.core.frozen_dict", "flax.linen", "jax.numpy", "flax.traverse_util", "jax.random", "flax", "jax"], "version_id": "1.16.0"}