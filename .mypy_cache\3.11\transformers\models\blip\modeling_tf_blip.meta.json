{"data_mtime": 1749061363, "dep_lines": [43, 44, 36, 25, 26, 35, 36, 17, 19, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.blip.configuration_blip", "transformers.models.blip.modeling_tf_blip_text", "transformers.utils.logging", "transformers.modeling_tf_outputs", "transformers.modeling_tf_utils", "transformers.tf_utils", "transformers.utils", "__future__", "warnings", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc", "collections", "logging", "transformers.activations_tf", "transformers.configuration_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "6bf2e0669b88b17b39784e98b9567b3b15d65d65", "id": "transformers.models.blip.modeling_tf_blip", "ignore_all": true, "interface_hash": "31c97e56e895ce09e965bd10b6e4e5ee86500646", "mtime": 1749058940, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\blip\\modeling_tf_blip.py", "plugin_data": null, "size": 71634, "suppressed": ["tensorflow"], "version_id": "1.16.0"}