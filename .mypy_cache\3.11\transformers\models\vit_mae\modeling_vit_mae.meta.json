{"data_mtime": 1749061351, "dep_lines": [32, 24, 31, 17, 24, 25, 27, 28, 29, 30, 31, 17, 18, 19, 20, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 20, 10, 5, 5, 5, 5, 5, 20, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.vit_mae.configuration_vit_mae", "torch.utils.checkpoint", "transformers.utils.logging", "collections.abc", "torch.utils", "torch.nn", "transformers.activations", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.pytorch_utils", "transformers.utils", "collections", "copy", "dataclasses", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.parameter", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "3fc959da78a1f34aa01e490679c9879629c87fd4", "id": "transformers.models.vit_mae.modeling_vit_mae", "ignore_all": true, "interface_hash": "4764fcb88b123c3ad50a1857ad17603ed82141fd", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vit_mae\\modeling_vit_mae.py", "plugin_data": null, "size": 48411, "suppressed": [], "version_id": "1.16.0"}