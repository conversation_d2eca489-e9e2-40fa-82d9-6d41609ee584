{".class": "MypyFile", "_fullname": "transformers.models.univnet.feature_extraction_univnet", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BatchFeature": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_utils.BatchFeature", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PaddingStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.PaddingStrategy", "kind": "Gdef", "module_public": false}, "SequenceFeatureExtractor": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_sequence_utils.SequenceFeatureExtractor", "kind": "Gdef", "module_public": false}, "TensorType": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.TensorType", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnivNetFeatureExtractor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.feature_extraction_sequence_utils.SequenceFeatureExtractor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", "name": "UnivNetFeatureExtractor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.univnet.feature_extraction_univnet", "mro": ["transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", "transformers.feature_extraction_sequence_utils.SequenceFeatureExtractor", "transformers.feature_extraction_utils.FeatureExtractionMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "raw_speech", "sampling_rate", "padding", "max_length", "truncation", "pad_to_multiple_of", "return_noise", "generator", "pad_end", "pad_length", "do_normalize", "return_attention_mask", "return_tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "raw_speech", "sampling_rate", "padding", "max_length", "truncation", "pad_to_multiple_of", "return_noise", "generator", "pad_end", "pad_length", "do_normalize", "return_attention_mask", "return_tensors"], "arg_types": ["transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.utils.generic.PaddingStrategy"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["numpy.random._generator.Generator", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of UnivNetFeatureExtractor", "ret_type": "transformers.feature_extraction_utils.BatchFeature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "feature_size", "sampling_rate", "padding_value", "do_normalize", "num_mel_bins", "hop_length", "win_length", "win_function", "filter_length", "max_length_s", "fmin", "fmax", "mel_floor", "center", "compression_factor", "compression_clip_val", "normalize_min", "normalize_max", "model_in_channels", "pad_end_length", "return_attention_mask", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "feature_size", "sampling_rate", "padding_value", "do_normalize", "num_mel_bins", "hop_length", "win_length", "win_function", "filter_length", "max_length_s", "fmin", "fmax", "mel_floor", "center", "compression_factor", "compression_clip_val", "normalize_min", "normalize_max", "model_in_channels", "pad_end_length", "return_attention_mask", "kwargs"], "arg_types": ["transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", "builtins.int", "builtins.int", "builtins.float", "builtins.bool", "builtins.int", "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.bool", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnivNetFeatureExtractor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "waveforms", "waveform_lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.batch_decode", "name": "batch_decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "waveforms", "waveform_lengths"], "arg_types": ["transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_decode of UnivNetFeatureExtractor", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "center": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.center", "name": "center", "setter_type": null, "type": "builtins.bool"}}, "compression_clip_val": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.compression_clip_val", "name": "compression_clip_val", "setter_type": null, "type": "builtins.float"}}, "compression_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.compression_factor", "name": "compression_factor", "setter_type": null, "type": "builtins.float"}}, "denormalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "spectrogram"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.denormalize", "name": "denormalize", "type": null}}, "do_normalize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.do_normalize", "name": "do_normalize", "setter_type": null, "type": "builtins.bool"}}, "filter_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.filter_length", "name": "filter_length", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fmax": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.fmax", "name": "fmax", "setter_type": null, "type": "builtins.float"}}, "fmin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.fmin", "name": "fmin", "setter_type": null, "type": "builtins.float"}}, "generate_noise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "noise_length", "generator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.generate_noise", "name": "generate_noise", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "noise_length", "generator"], "arg_types": ["transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", "builtins.int", {".class": "UnionType", "items": ["numpy.random._generator.Generator", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_noise of UnivNetFeatureExtractor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hop_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.hop_length", "name": "hop_length", "setter_type": null, "type": "builtins.int"}}, "max_length_s": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.max_length_s", "name": "max_length_s", "setter_type": null, "type": "builtins.int"}}, "mel_filters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.mel_filters", "name": "mel_filters", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "mel_floor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.mel_floor", "name": "mel_floor", "setter_type": null, "type": "builtins.float"}}, "mel_spectrogram": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "waveform"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.mel_spectrogram", "name": "mel_spectrogram", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "waveform"], "arg_types": ["transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mel_spectrogram of UnivNetFeatureExtractor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_in_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.model_in_channels", "name": "model_in_channels", "setter_type": null, "type": "builtins.int"}}, "model_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.model_input_names", "name": "model_input_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "n_fft": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.n_fft", "name": "n_fft", "setter_type": null, "type": "builtins.int"}}, "n_freqs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.n_freqs", "name": "n_freqs", "setter_type": null, "type": "builtins.int"}}, "normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "spectrogram"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.normalize", "name": "normalize", "type": null}}, "normalize_max": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.normalize_max", "name": "normalize_max", "setter_type": null, "type": "builtins.float"}}, "normalize_min": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.normalize_min", "name": "normalize_min", "setter_type": null, "type": "builtins.float"}}, "num_max_samples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.num_max_samples", "name": "num_max_samples", "setter_type": null, "type": "builtins.int"}}, "num_mel_bins": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.num_mel_bins", "name": "num_mel_bins", "setter_type": null, "type": "builtins.int"}}, "pad_end_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.pad_end_length", "name": "pad_end_length", "setter_type": null, "type": "builtins.int"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of UnivNetFeatureExtractor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "win_function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.win_function", "name": "win_function", "setter_type": null, "type": "builtins.str"}}, "win_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.win_length", "name": "win_length", "setter_type": null, "type": "builtins.int"}}, "window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.window", "name": "window", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.univnet.feature_extraction_univnet.UnivNetFeatureExtractor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.univnet.feature_extraction_univnet.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.univnet.feature_extraction_univnet.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.univnet.feature_extraction_univnet.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.univnet.feature_extraction_univnet.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.univnet.feature_extraction_univnet.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.univnet.feature_extraction_univnet.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.univnet.feature_extraction_univnet.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.univnet.feature_extraction_univnet.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "mel_filter_bank": {".class": "SymbolTableNode", "cross_ref": "transformers.audio_utils.mel_filter_bank", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "optimal_fft_length": {".class": "SymbolTableNode", "cross_ref": "transformers.audio_utils.optimal_fft_length", "kind": "Gdef", "module_public": false}, "spectrogram": {".class": "SymbolTableNode", "cross_ref": "transformers.audio_utils.spectrogram", "kind": "Gdef", "module_public": false}, "window_function": {".class": "SymbolTableNode", "cross_ref": "transformers.audio_utils.window_function", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\univnet\\feature_extraction_univnet.py"}