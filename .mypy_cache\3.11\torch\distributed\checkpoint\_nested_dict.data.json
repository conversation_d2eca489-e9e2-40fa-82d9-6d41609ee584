{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint._nested_dict", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FLATTEN_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.checkpoint._nested_dict.FLATTEN_MAPPING", "line": 23, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._traverse.OBJ_PATH"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "OBJ_PATH": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.OBJ_PATH", "kind": "Gdef"}, "STATE_DICT_ITEM": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.STATE_DICT_ITEM", "kind": "Gdef"}, "STATE_DICT_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._nested_dict.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._nested_dict.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._nested_dict.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._nested_dict.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._nested_dict.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._nested_dict.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_version": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._version", "kind": "Gdef"}, "flatten_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._nested_dict.flatten_state_dict", "name": "flatten_state_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state_dict"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flatten_state_dict", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._nested_dict.FLATTEN_MAPPING"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_element": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.set_element", "kind": "Gdef"}, "traverse_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.traverse_state_dict", "kind": "Gdef"}, "traverse_state_dict_v_2_3": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.traverse_state_dict_v_2_3", "kind": "Gdef"}, "unflatten_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state_dict", "mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._nested_dict.unflatten_state_dict", "name": "unflatten_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state_dict", "mapping"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._nested_dict.FLATTEN_MAPPING"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unflatten_state_dict", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_nested_dict.py"}