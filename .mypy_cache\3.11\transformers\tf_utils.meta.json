{"data_mtime": 1749061347, "dep_lines": [22, 20, 21, 22, 15, 17, 1, 1, 1, 1, 18], "dep_prios": [10, 5, 5, 20, 5, 10, 5, 30, 30, 30, 10], "dependencies": ["transformers.utils.logging", "transformers.feature_extraction_utils", "transformers.tokenization_utils_base", "transformers.utils", "typing", "numpy", "builtins", "_frozen_importlib", "abc", "logging"], "hash": "c0e26ce2d6b76b29b2e05ec5c24ee6c328c8dfa9", "id": "transformers.tf_utils", "ignore_all": true, "interface_hash": "dd6fdde60d5836f929bd7efe7ec099e4067a55c5", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\tf_utils.py", "plugin_data": null, "size": 11390, "suppressed": ["tensorflow"], "version_id": "1.16.0"}