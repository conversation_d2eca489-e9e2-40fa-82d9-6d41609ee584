{"data_mtime": 1749061346, "dep_lines": [28, 26, 27, 28, 17, 18, 19, 20, 21, 22, 24, 46, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 50, 48, 50], "dep_prios": [10, 5, 5, 5, 10, 10, 10, 5, 5, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25, 25], "dependencies": ["transformers.utils.logging", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils", "json", "os", "warnings", "dataclasses", "itertools", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "_io", "_typeshed", "_warnings", "abc", "collections", "enum", "io", "json.decoder", "logging", "torch._C", "torch._tensor", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "6f94fc910bc76f6e0d03aa1354f6a2b459d5fdf0", "id": "transformers.models.wav2vec2.tokenization_wav2vec2", "ignore_all": true, "interface_hash": "2165ff7a1598a7d0d6dd33078f5d6d80a9a66357", "mtime": 1749058954, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\wav2vec2\\tokenization_wav2vec2.py", "plugin_data": null, "size": 38839, "suppressed": ["jax.numpy", "tensorflow", "jax"], "version_id": "1.16.0"}