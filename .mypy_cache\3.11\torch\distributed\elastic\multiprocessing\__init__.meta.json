{"data_mtime": 1749061368, "dep_lines": [68, 82, 66, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["torch.distributed.elastic.multiprocessing.api", "torch.distributed.elastic.utils.logging", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "a2216ac860c0e9fc0a1e60e1d1c2fc9e1358a407", "id": "torch.distributed.elastic.multiprocessing", "ignore_all": true, "interface_hash": "cdc2c7940ac53ff947a4f9c06da47aca33b88b58", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\elastic\\multiprocessing\\__init__.py", "plugin_data": null, "size": 7612, "suppressed": [], "version_id": "1.16.0"}