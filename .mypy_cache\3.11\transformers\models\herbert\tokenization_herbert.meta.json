{"data_mtime": 1749061347, "dep_lines": [22, 21, 22, 15, 16, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 323, 412], "dep_prios": [10, 5, 20, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["transformers.utils.logging", "transformers.tokenization_utils", "transformers.utils", "json", "os", "re", "unicodedata", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "logging", "posixpath", "transformers.tokenization_utils_base", "transformers.utils.hub"], "hash": "92ff56c07bc08100d41ba5375e155f604d72bf14", "id": "transformers.models.herbert.tokenization_herbert", "ignore_all": true, "interface_hash": "eb848588f28ca2c5b0cc835b55394586169991eb", "mtime": 1749058946, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\herbert\\tokenization_herbert.py", "plugin_data": null, "size": 25067, "suppressed": ["sacremoses", "Mykytea"], "version_id": "1.16.0"}