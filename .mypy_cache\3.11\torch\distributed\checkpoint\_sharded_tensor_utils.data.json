{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint._sharded_tensor_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "OBJ_PATH": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.OBJ_PATH", "kind": "Gdef"}, "STATE_DICT_ITEM": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.STATE_DICT_ITEM", "kind": "Gdef"}, "STATE_DICT_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE", "kind": "Gdef"}, "Shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.shard.Shard", "kind": "Gdef"}, "ShardMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.metadata.ShardMetadata", "kind": "Gdef"}, "ShardedTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.api.ShardedTensor", "kind": "Gdef"}, "ShardedTensorMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.metadata.ShardedTensorMetadata", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._sharded_tensor_utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._sharded_tensor_utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._sharded_tensor_utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._sharded_tensor_utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._sharded_tensor_utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._sharded_tensor_utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_element_wise_add": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._element_wise_add", "kind": "Gdef"}, "_flatten_sharded_tensors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._sharded_tensor_utils._flatten_sharded_tensors", "name": "_flatten_sharded_tensors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state_dict"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_flatten_sharded_tensors", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_device_info": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._normalize_device_info", "kind": "Gdef"}, "_remote_device": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.remote_device._remote_device", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "set_element": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.set_element", "kind": "Gdef"}, "traverse_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.traverse_state_dict", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_sharded_tensor_utils.py"}