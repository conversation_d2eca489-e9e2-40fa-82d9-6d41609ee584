{"data_mtime": 1749061352, "dep_lines": [37, 36, 22, 24, 25, 26, 27, 34, 35, 36, 18, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.time_series_transformer.configuration_time_series_transformer", "transformers.utils.logging", "torch.nn", "transformers.activations", "transformers.cache_utils", "transformers.modeling_attn_mask_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.time_series_utils", "transformers.utils", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "collections", "enum", "logging", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "numpy.core", "numpy.core.multiarray", "torch._C", "torch._C._VariableFunctions", "torch._jit_internal", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.distributions", "torch.distributions.distribution", "torch.jit", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.sparse", "torch.nn.parameter", "torch.types", "torch.utils", "torch.utils._contextlib", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "58d495f78315ca938861a54cfa1010b28af92110", "id": "transformers.models.time_series_transformer.modeling_time_series_transformer", "ignore_all": true, "interface_hash": "05563ff0274df33a17a0f0b7752eaaba23c44712", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\time_series_transformer\\modeling_time_series_transformer.py", "plugin_data": null, "size": 91917, "suppressed": [], "version_id": "1.16.0"}