{".class": "MypyFile", "_fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "TFAdaptiveSoftmaxMask": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask", "name": "TFAdaptiveSoftmaxMask", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "vocab_size", "d_embed", "d_proj", "cutoffs", "div_val", "keep_order", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.__init__", "name": "__init__", "type": null}}, "_gather_logprob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["logprob", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask._gather_logprob", "name": "_gather_logprob", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask._gather_logprob", "name": "_gather_logprob", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["logprob", "target"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gather_logprob of TFAdaptiveSoftmaxMask", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_logit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["x", "W", "b", "proj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask._logit", "name": "_logit", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask._logit", "name": "_logit", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["x", "W", "b", "proj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_logit of TFAdaptiveSoftmaxMask", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "hidden", "target", "return_mean", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.call", "name": "call", "type": null}}, "cluster_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.cluster_bias", "name": "cluster_bias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cluster_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.cluster_weight", "name": "cluster_weight", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cutoff_ends": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.cutoff_ends", "name": "cutoff_ends", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cutoffs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.cutoffs", "name": "cutoffs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.d_embed", "name": "d_embed", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.d_proj", "name": "d_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "div_val": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.div_val", "name": "div_val", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.head_size", "name": "head_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "keep_order": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.keep_order", "name": "keep_order", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "n_clusters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.n_clusters", "name": "n_clusters", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.out_layers", "name": "out_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_projs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.out_projs", "name": "out_projs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "shortlist_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.shortlist_size", "name": "shortlist_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.vocab_size", "name": "vocab_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef"}, "shape_list": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.shape_list", "kind": "Gdef"}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.tf", "name": "tf", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.tf", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\deprecated\\transfo_xl\\modeling_tf_transfo_xl_utilities.py"}