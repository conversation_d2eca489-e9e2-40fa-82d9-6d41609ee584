{"data_mtime": 1749061352, "dep_lines": [47, 51, 38, 53, 23, 26, 27, 28, 29, 30, 36, 37, 38, 17, 18, 19, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.switch_transformers.configuration_switch_transformers", "torch.nn.attention.flex_attention", "transformers.utils.logging", "transformers.integrations.flex_attention", "torch.nn", "transformers.activations", "transformers.cache_utils", "transformers.generation", "transformers.modeling_attn_mask_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.pytorch_utils", "transformers.utils", "copy", "math", "warnings", "typing", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.attention", "torch.nn.functional", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.sparse", "torch.nn.parameter", "transformers.configuration_utils", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "8141a0054d96d8545af3af41b2c4b06e699a65fb", "id": "transformers.models.switch_transformers.modeling_switch_transformers", "ignore_all": true, "interface_hash": "788b28947f3346658d5ecf71695ea9e9e4f2cd01", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\switch_transformers\\modeling_switch_transformers.py", "plugin_data": null, "size": 88652, "suppressed": [], "version_id": "1.16.0"}