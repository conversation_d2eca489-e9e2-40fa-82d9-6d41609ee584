{"data_mtime": 1749061365, "dep_lines": [17, 16, 19, 25, 8, 15, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed._shard.sharded_tensor.shard", "torch.distributed._shard.sharded_tensor", "torch.distributed.checkpoint.api", "torch.distributed.checkpoint.metadata", "collections.abc", "torch.distributed", "cProfile", "inspect", "io", "itertools", "os", "warnings", "contextlib", "functools", "pstats", "typing", "torch", "builtins", "_frozen_importlib", "_io", "abc", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.distributed._shard", "torch.distributed._shard.sharded_tensor.api", "torch.distributed.distributed_c10d", "traceback"], "hash": "9353b0378920101780b49042119957015644196e", "id": "torch.distributed.checkpoint.utils", "ignore_all": true, "interface_hash": "167dee38f44a2716aec2a8d213b1bfea980447f2", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\utils.py", "plugin_data": null, "size": 16118, "suppressed": [], "version_id": "1.16.0"}