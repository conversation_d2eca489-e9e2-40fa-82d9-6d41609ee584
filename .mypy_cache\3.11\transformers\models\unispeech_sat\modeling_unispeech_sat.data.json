{".class": "MypyFile", "_fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2FN", "kind": "Gdef", "module_public": false}, "AMSoftmaxLoss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss", "name": "AMSoftmaxLoss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "input_dim", "num_labels", "scale", "margin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss.forward", "name": "forward", "type": null}}, "loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss.loss", "name": "loss", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "margin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss.margin", "name": "margin", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss.num_labels", "name": "num_labels", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss.scale", "name": "scale", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss.weight", "name": "weight", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.AMSoftmaxLoss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutput", "kind": "Gdef", "module_public": false}, "CausalLMOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.CausalLMOutput", "kind": "Gdef", "module_public": false}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.CrossEntropyLoss", "kind": "Gdef", "module_public": false}, "ModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ModelOutput", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "SequenceClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.SequenceClassifierOutput", "kind": "Gdef", "module_public": false}, "TDNNLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer", "name": "TDN<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "layer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dilation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.dilation", "name": "dilation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.in_conv_dim", "name": "in_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kernel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.kernel", "name": "kernel", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kernel_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.kernel_size", "name": "kernel_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.out_conv_dim", "name": "out_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.TDNNLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TokenClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.TokenClassifierOutput", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "UNISPEECH_SAT_ATTENTION_CLASSES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UNISPEECH_SAT_ATTENTION_CLASSES", "name": "UNISPEECH_SAT_ATTENTION_CLASSES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "UniSpeechSatAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "name": "UniSpeechSatAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "embed_dim", "num_heads", "dropout", "is_decoder", "bias", "is_causal", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "embed_dim", "num_heads", "dropout", "is_decoder", "bias", "is_causal", "config"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "builtins.int", "builtins.int", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.models.unispeech_sat.configuration_unispeech_sat.UniSpeechSatConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UniSpeechSatAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tensor", "seq_len", "bsz"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention._shape", "name": "_shape", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tensor", "seq_len", "bsz"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "torch._tensor.Tensor", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_shape of UniSpeechSatAttention", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.config", "name": "config", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.models.unispeech_sat.configuration_unispeech_sat.UniSpeechSatConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.dropout", "name": "dropout", "setter_type": null, "type": "builtins.float"}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.embed_dim", "name": "embed_dim", "setter_type": null, "type": "builtins.int"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "past_key_value", "attention_mask", "layer_head_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "past_key_value", "attention_mask", "layer_head_mask", "output_attentions"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.head_dim", "name": "head_dim", "setter_type": null, "type": "builtins.int"}}, "is_causal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.is_causal", "name": "is_causal", "setter_type": null, "type": "builtins.bool"}}, "is_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.is_decoder", "name": "is_decoder", "setter_type": null, "type": "builtins.bool"}}, "k_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.k_proj", "name": "k_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "num_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.num_heads", "name": "num_heads", "setter_type": null, "type": "builtins.int"}}, "out_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.out_proj", "name": "out_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "q_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.q_proj", "name": "q_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "scaling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.scaling", "name": "scaling", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "v_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.v_proj", "name": "v_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatAttnAdapterLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer", "name": "UniSpeechSatAttnAdapterLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.__init__", "name": "__init__", "type": null}}, "act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.act_fn", "name": "act_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatAttnAdapterLayer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hidden_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.hidden_dim", "name": "hidden_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "input_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.input_dim", "name": "input_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.linear_1", "name": "linear_1", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.linear_2", "name": "linear_2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.norm", "name": "norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttnAdapterLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatBaseModelOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatBaseModelOutput", "line": 1226, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "transformers.modeling_outputs.Wav2Vec2BaseModelOutput"}}, "UniSpeechSatConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.unispeech_sat.configuration_unispeech_sat.UniSpeechSatConfig", "kind": "Gdef", "module_public": false}, "UniSpeechSatEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder", "name": "UniSpeechSatEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.__init__", "name": "__init__", "type": null}}, "_use_flash_attention_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder._use_flash_attention_2", "name": "_use_flash_attention_2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "torch.tensor"}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatEncoder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos_conv_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.pos_conv_embed", "name": "pos_conv_embed", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatEncoderLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer", "name": "UniSpeechSatEncoderLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer.__init__", "name": "__init__", "type": null}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer.attention", "name": "attention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feed_forward": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer.feed_forward", "name": "feed_forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "final_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer.final_layer_norm", "name": "final_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer.forward", "name": "forward", "type": null}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatEncoderLayerStableLayerNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm", "name": "UniSpeechSatEncoderLayerStableLayerNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.__init__", "name": "__init__", "type": null}}, "adapter_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.adapter_layer", "name": "adapter_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.attention", "name": "attention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feed_forward": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.feed_forward", "name": "feed_forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "final_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.final_layer_norm", "name": "final_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatEncoderLayerStableLayerNorm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderLayerStableLayerNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatEncoderStableLayerNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm", "name": "UniSpeechSatEncoderStableLayerNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.__init__", "name": "__init__", "type": null}}, "_use_flash_attention_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm._use_flash_attention_2", "name": "_use_flash_attention_2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.forward", "name": "forward", "type": null}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos_conv_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.pos_conv_embed", "name": "pos_conv_embed", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatFeatureEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder", "name": "UniSpeechSatFeatureEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder.__init__", "name": "__init__", "type": null}}, "_freeze_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder._freeze_parameters", "name": "_freeze_parameters", "type": null}}, "_requires_grad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder._requires_grad", "name": "_requires_grad", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder.conv_layers", "name": "conv_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder.forward", "name": "forward", "type": null}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatFeatureProjection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection", "name": "UniSpeechSatFeatureProjection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection.__init__", "name": "__init__", "type": null}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection.forward", "name": "forward", "type": null}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection.projection", "name": "projection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatFeedForward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward", "name": "UniSpeechSatFeedForward", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward.forward", "name": "forward", "type": null}}, "intermediate_act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward.intermediate_act_fn", "name": "intermediate_act_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward.intermediate_dense", "name": "intermediate_dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward.intermediate_dropout", "name": "intermediate_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward.output_dense", "name": "output_dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward.output_dropout", "name": "output_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeedForward", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatFlashAttention2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2", "name": "UniSpeechSatFlashAttention2", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2", "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2.__init__", "name": "__init__", "type": null}}, "_flash_attn_uses_top_left_mask": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2._flash_attn_uses_top_left_mask", "name": "_flash_attn_uses_top_left_mask", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_reshape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tensor", "seq_len", "bsz"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2._reshape", "name": "_reshape", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tensor", "seq_len", "bsz"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2", "torch._tensor.Tensor", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reshape of UniSpeechSatFlashAttention2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "past_key_value", "attention_mask", "layer_head_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "past_key_value", "attention_mask", "layer_head_mask", "output_attentions"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatFlashAttention2", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFlashAttention2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatForAudioFrameClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification", "name": "UniSpeechSatForAudioFrameClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification", "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.__init__", "name": "__init__", "type": null}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatForAudioFrameClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.TokenClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freeze_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.freeze_base_model", "name": "freeze_base_model", "type": null}}, "freeze_feature_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.freeze_feature_encoder", "name": "freeze_feature_encoder", "type": null}}, "freeze_feature_extractor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.freeze_feature_extractor", "name": "freeze_feature_extractor", "type": null}}, "layer_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.layer_weights", "name": "layer_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.num_labels", "name": "num_labels", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unispeech_sat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.unispeech_sat", "name": "unispeech_sat", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForAudioFrameClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatForCTC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC", "name": "UniSpeechSatForCTC", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC", "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "target_lang"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "target_lang"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UniSpeechSatForCTC", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.dropout", "name": "dropout", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatForCTC", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.CausalLMOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freeze_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.freeze_base_model", "name": "freeze_base_model", "type": null}}, "freeze_feature_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.freeze_feature_encoder", "name": "freeze_feature_encoder", "type": null}}, "freeze_feature_extractor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.freeze_feature_extractor", "name": "freeze_feature_extractor", "type": null}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.lm_head", "name": "lm_head", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "target_lang": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.target_lang", "name": "target_lang", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tie_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.tie_weights", "name": "tie_weights", "type": null}}, "unispeech_sat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.unispeech_sat", "name": "unispeech_sat", "setter_type": null, "type": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForCTC", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatForPreTraining": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining", "name": "UniSpeechSatForPreTraining", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining", "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining", "transformers.models.unispeech_sat.configuration_unispeech_sat.UniSpeechSatConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UniSpeechSatForPreTraining", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_contrastive_logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["target_features", "negative_features", "predicted_features", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.compute_contrastive_logits", "name": "compute_contrastive_logits", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["target_features", "negative_features", "predicted_features", "temperature"], "arg_types": ["torch._<PERSON><PERSON>", "torch._<PERSON><PERSON>", "torch._<PERSON><PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_contrastive_logits of UniSpeechSatForPreTraining", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.compute_contrastive_logits", "name": "compute_contrastive_logits", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["target_features", "negative_features", "predicted_features", "temperature"], "arg_types": ["torch._<PERSON><PERSON>", "torch._<PERSON><PERSON>", "torch._<PERSON><PERSON>", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_contrastive_logits of UniSpeechSatForPreTraining", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.dropout", "name": "dropout", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "dropout_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.dropout_features", "name": "dropout_features", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatForPreTraining", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freeze_feature_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.freeze_feature_encoder", "name": "freeze_feature_encoder", "type": null}}, "freeze_feature_extractor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.freeze_feature_extractor", "name": "freeze_feature_extractor", "type": null}}, "label_embeddings_concat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.label_embeddings_concat", "name": "label_embeddings_concat", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "layer_norm_for_extract": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.layer_norm_for_extract", "name": "layer_norm_for_extract", "setter_type": null, "type": "torch.nn.modules.normalization.LayerNorm"}}, "project_hid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.project_hid", "name": "project_hid", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "project_q": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.project_q", "name": "project_q", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "quantizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.quantizer", "name": "quantizer", "setter_type": null, "type": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer"}}, "set_gumbel_temperature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.set_gumbel_temperature", "name": "set_gumbel_temperature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "temperature"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_gumbel_temperature of UniSpeechSatForPreTraining", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "speaker_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.speaker_proj", "name": "speaker_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "unispeech_sat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.unispeech_sat", "name": "unispeech_sat", "setter_type": null, "type": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTraining", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatForPreTrainingOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput", "name": "UniSpeechSatForPreTrainingOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 70, "name": "loss", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 71, "name": "logits", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 72, "name": "projected_states", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "projected_quantized_states", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "codevector_perplexity", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 76, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "loss", "logits", "projected_states", "projected_quantized_states", "codevector_perplexity", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "loss", "logits", "projected_states", "projected_quantized_states", "codevector_perplexity", "hidden_states", "attentions"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UniSpeechSatForPreTrainingOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "loss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "projected_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "projected_quantized_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "codevector_perplexity"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "projected_states", "projected_quantized_states", "codevector_perplexity", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "projected_states", "projected_quantized_states", "codevector_perplexity", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of UniSpeechSatForPreTrainingOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "projected_states", "projected_quantized_states", "codevector_perplexity", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of UniSpeechSatForPreTrainingOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.attentions", "name": "attentions", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "codevector_perplexity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.codevector_perplexity", "name": "codevector_perplexity", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.hidden_states", "name": "hidden_states", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.logits", "name": "logits", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.loss", "name": "loss", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "projected_quantized_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.projected_quantized_states", "name": "projected_quantized_states", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "projected_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.projected_states", "name": "projected_states", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForPreTrainingOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatForSequenceClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification", "name": "UniSpeechSatForSequenceClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification", "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.__init__", "name": "__init__", "type": null}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatForSequenceClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.SequenceClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freeze_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.freeze_base_model", "name": "freeze_base_model", "type": null}}, "freeze_feature_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.freeze_feature_encoder", "name": "freeze_feature_encoder", "type": null}}, "freeze_feature_extractor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.freeze_feature_extractor", "name": "freeze_feature_extractor", "type": null}}, "layer_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.layer_weights", "name": "layer_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.projector", "name": "projector", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unispeech_sat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.unispeech_sat", "name": "unispeech_sat", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForSequenceClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatForXVector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector", "name": "UniSpeechSatForXVector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector", "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.__init__", "name": "__init__", "type": null}}, "_get_tdnn_output_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector._get_tdnn_output_lengths", "name": "_get_tdnn_output_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input_lengths"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_tdnn_output_lengths of UniSpeechSatForXVector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feature_extractor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.feature_extractor", "name": "feature_extractor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatForXVector", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.XVectorOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freeze_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.freeze_base_model", "name": "freeze_base_model", "type": null}}, "freeze_feature_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.freeze_feature_encoder", "name": "freeze_feature_encoder", "type": null}}, "freeze_feature_extractor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.freeze_feature_extractor", "name": "freeze_feature_extractor", "type": null}}, "layer_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.layer_weights", "name": "layer_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "objective": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.objective", "name": "objective", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.projector", "name": "projector", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tdnn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.tdnn", "name": "tdnn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unispeech_sat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.unispeech_sat", "name": "unispeech_sat", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatForXVector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatGroupNormConvLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer", "name": "UniSpeechSatGroupNormConvLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "layer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer.conv", "name": "conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer.forward", "name": "forward", "type": null}}, "in_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer.in_conv_dim", "name": "in_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer.out_conv_dim", "name": "out_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGroupNormConvLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatGumbelVectorQuantizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer", "name": "UniSpeechSatGumbelVectorQuantizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer.__init__", "name": "__init__", "type": null}}, "_compute_perplexity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["probs", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer._compute_perplexity", "name": "_compute_perplexity", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer._compute_perplexity", "name": "_compute_perplexity", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["probs", "mask"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_perplexity of UniSpeechSatGumbelVectorQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "codevectors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer.codevectors", "name": "codevectors", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer.forward", "name": "forward", "type": null}}, "num_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer.num_groups", "name": "num_groups", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_vars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer.num_vars", "name": "num_vars", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "temperature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer.temperature", "name": "temperature", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer.weight_proj", "name": "weight_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatGumbelVectorQuantizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatLayerNormConvLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer", "name": "UniSpeechSatLayerNormConvLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "layer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer.conv", "name": "conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer.forward", "name": "forward", "type": null}}, "in_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer.in_conv_dim", "name": "in_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer.out_conv_dim", "name": "out_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatLayerNormConvLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel", "name": "UniSpeechSatModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel", "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel", "transformers.models.unispeech_sat.configuration_unispeech_sat.UniSpeechSatConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UniSpeechSatModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mask_hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "mask_time_indices", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel._mask_hidden_states", "name": "_mask_hidden_states", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "mask_time_indices", "attention_mask"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel", "torch._<PERSON><PERSON>", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mask_hidden_states of UniSpeechSatModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel.encoder", "name": "encoder", "setter_type": null, "type": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatEncoderStableLayerNorm"}}, "feature_extractor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel.feature_extractor", "name": "feature_extractor", "setter_type": null, "type": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureEncoder"}}, "feature_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel.feature_projection", "name": "feature_projection", "setter_type": null, "type": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatFeatureProjection"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.Wav2Vec2BaseModelOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "masked_spec_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel.masked_spec_embed", "name": "masked_spec_embed", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatNoLayerNormConvLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer", "name": "UniSpeechSatNoLayerNormConvLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "layer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer.conv", "name": "conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer.forward", "name": "forward", "type": null}}, "in_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer.in_conv_dim", "name": "in_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer.out_conv_dim", "name": "out_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatNoLayerNormConvLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatPositionalConvEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding", "name": "UniSpeechSatPositionalConvEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding.conv", "name": "conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding.forward", "name": "forward", "type": null}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding.padding", "name": "padding", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPositionalConvEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "name": "UniSpeechSatPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_get_feat_extract_output_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel._get_feat_extract_output_lengths", "name": "_get_feat_extract_output_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input_lengths"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feat_extract_output_lengths of UniSpeechSatPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_feature_vector_attention_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "feature_vector_length", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel._get_feature_vector_attention_mask", "name": "_get_feature_vector_attention_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "feature_vector_length", "attention_mask"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "builtins.int", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feature_vector_attention_mask of UniSpeechSatPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel._init_weights", "name": "_init_weights", "type": null}}, "_supports_flash_attn_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel._supports_flash_attn_2", "name": "_supports_flash_attn_2", "setter_type": null, "type": "builtins.bool"}}, "_supports_sdpa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel._supports_sdpa", "name": "_supports_sdpa", "setter_type": null, "type": "builtins.bool"}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "hidden_size", "num_hidden_layers", "num_attention_heads", "intermediate_size", "hidden_act", "hidden_dropout", "activation_dropout", "attention_dropout", "feat_proj_dropout", "feat_quantizer_dropout", "final_dropout", "layerdrop", "initializer_range", "layer_norm_eps", "feat_extract_norm", "feat_extract_activation", "conv_dim", "conv_stride", "conv_kernel", "conv_bias", "num_conv_pos_embeddings", "num_conv_pos_embedding_groups", "do_stable_layer_norm", "apply_spec_augment", "mask_time_prob", "mask_time_length", "mask_time_min_masks", "mask_feature_prob", "mask_feature_length", "mask_feature_min_masks", "num_codevectors_per_group", "num_codevector_groups", "contrastive_logits_temperature", "num_negatives", "codevector_dim", "proj_codevector_dim", "diversity_loss_weight", "ctc_loss_reduction", "ctc_zero_infinity", "use_weighted_layer_sum", "classifier_proj_size", "tdnn_dim", "tdnn_kernel", "tdnn_dilation", "xvector_output_dim", "pad_token_id", "bos_token_id", "eos_token_id", "num_clusters", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.unispeech_sat.configuration_unispeech_sat.UniSpeechSatConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.unispeech_sat.configuration_unispeech_sat.UniSpeechSatConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel.main_input_name", "name": "main_input_name", "setter_type": null, "type": "builtins.str"}}, "supports_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel.supports_gradient_checkpointing", "name": "supports_gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatSamePadLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSamePadLayer", "name": "UniSpeechSatSamePadLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSamePadLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSamePadLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "num_conv_pos_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSamePadLayer.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSamePadLayer.forward", "name": "forward", "type": null}}, "num_pad_remove": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSamePadLayer.num_pad_remove", "name": "num_pad_remove", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSamePadLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSamePadLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UniSpeechSatSdpaAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSdpaAttention", "name": "UniSpeechSatSdpaAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSdpaAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.unispeech_sat.modeling_unispeech_sat", "mro": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSdpaAttention", "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "past_key_value", "attention_mask", "layer_head_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSdpaAttention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "past_key_value", "attention_mask", "layer_head_mask", "output_attentions"], "arg_types": ["transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSdpaAttention", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of UniSpeechSatSdpaAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSdpaAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.unispeech_sat.modeling_unispeech_sat.UniSpeechSatSdpaAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Wav2Vec2BaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.Wav2Vec2BaseModelOutput", "kind": "Gdef", "module_public": false}, "XVectorOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.XVectorOutput", "kind": "Gdef", "module_public": false}, "_HIDDEN_STATES_START_POSITION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat._HIDDEN_STATES_START_POSITION", "name": "_HIDDEN_STATES_START_POSITION", "setter_type": null, "type": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_compute_mask_indices": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["shape", "mask_prob", "mask_length", "attention_mask", "min_masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat._compute_mask_indices", "name": "_compute_mask_indices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["shape", "mask_prob", "mask_length", "attention_mask", "min_masks"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.int", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_mask_indices", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flash_attention_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flash_attention_utils._flash_attention_forward", "kind": "Gdef", "module_public": false}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "flash_attn_supports_top_left_mask": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flash_attention_utils.flash_attn_supports_top_left_mask", "kind": "Gdef", "module_public": false}, "is_deepspeed_zero3_enabled": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.deepspeed.is_deepspeed_zero3_enabled", "kind": "Gdef", "module_public": false}, "is_flash_attn_available": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flash_attention_utils.is_flash_attn_available", "kind": "Gdef", "module_public": false}, "is_fsdp_managed_module": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.fsdp.is_fsdp_managed_module", "kind": "Gdef", "module_public": false}, "is_peft_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_peft_available", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.unispeech_sat.modeling_unispeech_sat.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\unispeech_sat\\modeling_unispeech_sat.py"}