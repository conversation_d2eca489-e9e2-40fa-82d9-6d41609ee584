{"data_mtime": 1749061347, "dep_lines": [40, 41, 20, 27, 28, 40, 17, 18, 19, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.utils.logging", "transformers.utils.import_utils", "collections.abc", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils", "itertools", "json", "os", "shutil", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "collections", "enum", "io", "json.decoder", "logging", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "845d485ba74a80132ab2f3db0be5bab7b0ed6ff2", "id": "transformers.models.mluke.tokenization_mluke", "ignore_all": true, "interface_hash": "06fd9f7d1310ea91fa04501bdad251e2d2f466e3", "mtime": 1749058950, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\mluke\\tokenization_mluke.py", "plugin_data": null, "size": 82179, "suppressed": ["sentencepiece"], "version_id": "1.16.0"}