{"data_mtime": 1749061348, "dep_lines": [38, 24, 25, 26, 33, 34, 19, 20, 22, 193, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 187, 187, 199], "dep_prios": [25, 5, 5, 5, 5, 5, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20], "dependencies": ["transformers.models.owlvit.modeling_owlvit", "transformers.image_processing_utils", "transformers.image_utils", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.utils", "warnings", "typing", "numpy", "torch", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "abc", "collections", "enum", "torch._C", "torch._tensor", "transformers.feature_extraction_utils", "transformers.image_processing_base", "transformers.utils.generic", "transformers.utils.hub"], "hash": "55667792b563c55b1283fff34b5834e043d8738a", "id": "transformers.models.owlvit.processing_owlvit", "ignore_all": true, "interface_hash": "28edbc2bbac525783c365a9a0c4a516a1af6fcf0", "mtime": 1749058950, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\owlvit\\processing_owlvit.py", "plugin_data": null, "size": 17015, "suppressed": ["jax.numpy", "jax", "tensorflow"], "version_id": "1.16.0"}