{".class": "MypyFile", "_fullname": "transformers.onnx.features", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModel", "kind": "Gdef"}, "AutoModelForCausalLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForCausalLM", "kind": "Gdef"}, "AutoModelForImageClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForImageClassification", "kind": "Gdef"}, "AutoModelForImageSegmentation": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForImageSegmentation", "kind": "Gdef"}, "AutoModelForMaskedImageModeling": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForMaskedImageModeling", "kind": "Gdef"}, "AutoModelForMaskedLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForMaskedLM", "kind": "Gdef"}, "AutoModelForMultipleChoice": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForMultipleChoice", "kind": "Gdef"}, "AutoModelForObjectDetection": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForObjectDetection", "kind": "Gdef"}, "AutoModelForQuestionAnswering": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForQuestionAnswering", "kind": "Gdef"}, "AutoModelForSemanticSegmentation": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForSemanticSegmentation", "kind": "Gdef"}, "AutoModelForSeq2SeqLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForSeq2SeqLM", "kind": "Gdef"}, "AutoModelForSequenceClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForSequenceClassification", "kind": "Gdef"}, "AutoModelForSpeechSeq2Seq": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForSpeechSeq2Seq", "kind": "Gdef"}, "AutoModelForTokenClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForTokenClassification", "kind": "Gdef"}, "AutoModelForVision2Seq": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForVision2Seq", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FeaturesManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.onnx.features.FeaturesManager", "name": "FeaturesManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.onnx.features.FeaturesManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.onnx.features", "mro": ["transformers.onnx.features.FeaturesManager", "builtins.object"], "names": {".class": "SymbolTable", "AVAILABLE_FEATURES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.onnx.features.FeaturesManager.AVAILABLE_FEATURES", "name": "AVAILABLE_FEATURES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_SUPPORTED_MODEL_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.onnx.features.FeaturesManager._SUPPORTED_MODEL_TYPE", "name": "_SUPPORTED_MODEL_TYPE", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.configuration_utils.PretrainedConfig"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.onnx.config.OnnxConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_TASKS_TO_AUTOMODELS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.onnx.features.FeaturesManager._TASKS_TO_AUTOMODELS", "name": "_TASKS_TO_AUTOMODELS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.auto.modeling_auto.AutoModelForSpeechSeq2Seq"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.auto.auto_factory._BaseAutoModelClass", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_TASKS_TO_TF_AUTOMODELS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.onnx.features.FeaturesManager._TASKS_TO_TF_AUTOMODELS", "name": "_TASKS_TO_TF_AUTOMODELS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.auto.modeling_tf_auto.TFAutoModelForSemanticSegmentation"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.auto.auto_factory._BaseAutoModelClass", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_validate_framework_choice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["framework"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.onnx.features.FeaturesManager._validate_framework_choice", "name": "_validate_framework_choice", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["framework"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_framework_choice of FeaturesManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.onnx.features.FeaturesManager._validate_framework_choice", "name": "_validate_framework_choice", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["framework"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_framework_choice of FeaturesManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "check_supported_model_or_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["model", "feature"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.onnx.features.FeaturesManager.check_supported_model_or_raise", "name": "check_supported_model_or_raise", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model", "feature"], "arg_types": [{".class": "UnionType", "items": ["transformers.utils.dummy_pt_objects.PreTrainedModel", "transformers.utils.dummy_tf_objects.TFPreTrainedModel"], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_supported_model_or_raise of FeaturesManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.onnx.features.FeaturesManager.check_supported_model_or_raise", "name": "check_supported_model_or_raise", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model", "feature"], "arg_types": [{".class": "UnionType", "items": ["transformers.utils.dummy_pt_objects.PreTrainedModel", "transformers.utils.dummy_tf_objects.TFPreTrainedModel"], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_supported_model_or_raise of FeaturesManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "determine_framework": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["model", "framework"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.onnx.features.FeaturesManager.determine_framework", "name": "determine_framework", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model", "framework"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "determine_framework of FeaturesManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.onnx.features.FeaturesManager.determine_framework", "name": "determine_framework", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model", "framework"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "determine_framework of FeaturesManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "feature_to_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["feature"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.onnx.features.FeaturesManager.feature_to_task", "name": "feature_to_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["feature"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feature_to_task of FeaturesManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.onnx.features.FeaturesManager.feature_to_task", "name": "feature_to_task", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["feature"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feature_to_task of FeaturesManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model_type", "feature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.onnx.features.FeaturesManager.get_config", "name": "get_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["model_type", "feature"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "model_type"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_config of FeaturesManager", "ret_type": "transformers.onnx.config.OnnxConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_class_for_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["feature", "framework"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.onnx.features.FeaturesManager.get_model_class_for_feature", "name": "get_model_class_for_feature", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["feature", "framework"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_model_class_for_feature of FeaturesManager", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.onnx.features.FeaturesManager.get_model_class_for_feature", "name": "get_model_class_for_feature", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["feature", "framework"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_model_class_for_feature of FeaturesManager", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_model_from_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["feature", "model", "framework", "cache_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.onnx.features.FeaturesManager.get_model_from_feature", "name": "get_model_from_feature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["feature", "model", "framework", "cache_dir"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_model_from_feature of FeaturesManager", "ret_type": {".class": "UnionType", "items": ["transformers.utils.dummy_pt_objects.PreTrainedModel", "transformers.utils.dummy_tf_objects.TFPreTrainedModel"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.onnx.features.FeaturesManager.get_model_from_feature", "name": "get_model_from_feature", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["feature", "model", "framework", "cache_dir"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_model_from_feature of FeaturesManager", "ret_type": {".class": "UnionType", "items": ["transformers.utils.dummy_pt_objects.PreTrainedModel", "transformers.utils.dummy_tf_objects.TFPreTrainedModel"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_supported_features_for_model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["model_type", "model_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.onnx.features.FeaturesManager.get_supported_features_for_model_type", "name": "get_supported_features_for_model_type", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model_type", "model_name"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_features_for_model_type of FeaturesManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.configuration_utils.PretrainedConfig"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.onnx.config.OnnxConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.onnx.features.FeaturesManager.get_supported_features_for_model_type", "name": "get_supported_features_for_model_type", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model_type", "model_name"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_features_for_model_type of FeaturesManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.configuration_utils.PretrainedConfig"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.onnx.config.OnnxConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.onnx.features.FeaturesManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.onnx.features.FeaturesManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnnxConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.onnx.config.OnnxConfig", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.dummy_pt_objects.PreTrainedModel", "kind": "Gdef"}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef"}, "TF2_WEIGHTS_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.TF2_WEIGHTS_NAME", "kind": "Gdef"}, "TFAutoModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModel", "kind": "Gdef"}, "TFAutoModelForCausalLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForCausalLM", "kind": "Gdef"}, "TFAutoModelForMaskedLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForMaskedLM", "kind": "Gdef"}, "TFAutoModelForMultipleChoice": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForMultipleChoice", "kind": "Gdef"}, "TFAutoModelForQuestionAnswering": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForQuestionAnswering", "kind": "Gdef"}, "TFAutoModelForSemanticSegmentation": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForSemanticSegmentation", "kind": "Gdef"}, "TFAutoModelForSeq2SeqLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForSeq2SeqLM", "kind": "Gdef"}, "TFAutoModelForSequenceClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForSequenceClassification", "kind": "Gdef"}, "TFAutoModelForTokenClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForTokenClassification", "kind": "Gdef"}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.dummy_tf_objects.TFPreTrainedModel", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WEIGHTS_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.WEIGHTS_NAME", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.onnx.features.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.onnx.features.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.onnx.features.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.onnx.features.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.onnx.features.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.onnx.features.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "is_tf_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_tf_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.onnx.features.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "reduce": {".class": "SymbolTableNode", "cross_ref": "functools.reduce", "kind": "Gdef"}, "supported_features_mapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5], "arg_names": ["supported_features", "onnx_config_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.onnx.features.supported_features_mapping", "name": "supported_features_mapping", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["supported_features", "onnx_config_cls"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supported_features_mapping", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.configuration_utils.PretrainedConfig"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.onnx.config.OnnxConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transformers": {".class": "SymbolTableNode", "cross_ref": "transformers", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\onnx\\features.py"}