{"data_mtime": 1749061380, "dep_lines": [9, 10, 16, 24, 25, 160, 2, 3, 4, 5, 58, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7], "dep_prios": [5, 5, 5, 5, 5, 20, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["torch.distributed.checkpoint._fsspec_filesystem", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.storage", "torch.futures", "safetensors.torch", "dataclasses", "json", "queue", "typing", "huggingface_hub", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "contextlib", "huggingface_hub.hf_file_system", "io", "json.decoder", "os", "torch._C", "torch.distributed.checkpoint._extension", "torch.distributed.checkpoint.filesystem", "torch.distributed.checkpoint.staging"], "hash": "8153baa3179fb44dda84f3a75fb5cb53f5817089", "id": "torch.distributed.checkpoint._hf_storage", "ignore_all": true, "interface_hash": "852f1643b061584953aee080e534780148ca95a5", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_hf_storage.py", "plugin_data": null, "size": 7433, "suppressed": ["fsspec"], "version_id": "1.16.0"}