{"data_mtime": 1749061339, "dep_lines": [18, 17, 18, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.configuration_utils", "transformers.utils", "builtins", "_frozen_importlib", "abc", "logging", "transformers.utils.hub", "typing"], "hash": "92a04e0797b20093dd15094606f5f7d5922af141", "id": "transformers.models.timesformer.configuration_timesformer", "ignore_all": true, "interface_hash": "f30d85f33c960ec7cc72a715d3fb93d5355b2d34", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\timesformer\\configuration_timesformer.py", "plugin_data": null, "size": 5568, "suppressed": [], "version_id": "1.16.0"}