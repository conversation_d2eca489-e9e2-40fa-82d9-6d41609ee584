{"data_mtime": 1749061366, "dep_lines": [6, 7, 13, 1, 2, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.futures", "abc", "os", "dataclasses", "typing", "builtins", "_frozen_importlib", "torch._C", "types"], "hash": "5bd37fdc374779b56382f932281820530ee3290c", "id": "torch.distributed.checkpoint.storage", "ignore_all": true, "interface_hash": "0a63f61139db5e4b5a4722518c16178e40c5a10d", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\storage.py", "plugin_data": null, "size": 10009, "suppressed": [], "version_id": "1.16.0"}