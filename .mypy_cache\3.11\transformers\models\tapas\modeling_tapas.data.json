{".class": "MypyFile", "_fullname": "transformers.models.tapas.modeling_tapas", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2FN", "kind": "Gdef", "module_public": false}, "AverageApproximationFunction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.AverageApproximationFunction", "name": "AverageApproximationFunction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "transformers.models.tapas.modeling_tapas.AverageApproximationFunction", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.AverageApproximationFunction", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "FIRST_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.AverageApproximationFunction.FIRST_ORDER", "name": "FIRST_ORDER", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "first_order"}, "type_ref": "builtins.str"}}}, "RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.AverageApproximationFunction.RATIO", "name": "RATIO", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ratio"}, "type_ref": "builtins.str"}}}, "SECOND_ORDER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.AverageApproximationFunction.SECOND_ORDER", "name": "SECOND_ORDER", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "second_order"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.AverageApproximationFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.AverageApproximationFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BCEWithLogitsLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.BCEWithLogitsLoss", "kind": "Gdef", "module_public": false}, "BaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutput", "kind": "Gdef", "module_public": false}, "BaseModelOutputWithPooling": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutputWithPooling", "kind": "Gdef", "module_public": false}, "CLOSE_ENOUGH_TO_LOG_ZERO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.CLOSE_ENOUGH_TO_LOG_ZERO", "name": "CLOSE_ENOUGH_TO_LOG_ZERO", "setter_type": null, "type": "builtins.float"}}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.CrossEntropyLoss", "kind": "Gdef", "module_public": false}, "EPSILON_ZERO_DIVISION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.EPSILON_ZERO_DIVISION", "name": "EPSILON_ZERO_DIVISION", "setter_type": null, "type": "builtins.float"}}, "IndexMap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.IndexMap", "name": "IndexMap", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.IndexMap", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.IndexMap", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "indices", "num_segments", "batch_dims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.IndexMap.__init__", "name": "__init__", "type": null}}, "batch_dims": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.IndexMap.batch_dims", "name": "batch_dims", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "batch_shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.IndexMap.batch_shape", "name": "batch_shape", "type": null}}, "indices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.IndexMap.indices", "name": "indices", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_segments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.IndexMap.num_segments", "name": "num_segments", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.IndexMap.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.IndexMap", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MSELoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.MSELoss", "kind": "Gdef", "module_public": false}, "MaskedLMOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.MaskedLMOutput", "kind": "Gdef", "module_public": false}, "ModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ModelOutput", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "ProductIndexMap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.tapas.modeling_tapas.IndexMap"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.ProductIndexMap", "name": "ProductIndexMap", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.ProductIndexMap", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.ProductIndexMap", "transformers.models.tapas.modeling_tapas.IndexMap", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "outer_index", "inner_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.ProductIndexMap.__init__", "name": "__init__", "type": null}}, "inner_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.ProductIndexMap.inner_index", "name": "inner_index", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "outer_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.ProductIndexMap.outer_index", "name": "outer_index", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "project_inner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.ProductIndexMap.project_inner", "name": "project_inner", "type": null}}, "project_outer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.ProductIndexMap.project_outer", "name": "project_outer", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.ProductIndexMap.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.ProductIndexMap", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SequenceClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.SequenceClassifierOutput", "kind": "Gdef", "module_public": false}, "TableQuestionAnsweringOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput", "name": "TableQuestionAnsweringOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 66, "name": "loss", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 67, "name": "logits", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 68, "name": "logits_aggregation", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 69, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 70, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "loss", "logits", "logits_aggregation", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "loss", "logits", "logits_aggregation", "hidden_states", "attentions"], "arg_types": ["transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TableQuestionAnsweringOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "loss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logits_aggregation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "logits_aggregation", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "logits_aggregation", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TableQuestionAnsweringOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "logits_aggregation", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TableQuestionAnsweringOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.attentions", "name": "attentions", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.hidden_states", "name": "hidden_states", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.logits", "name": "logits", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "logits_aggregation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.logits_aggregation", "name": "logits_aggregation", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.loss", "name": "loss", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention", "name": "TapasAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasAttention", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Tapas<PERSON>tten<PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention.output", "name": "output", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention.prune_heads", "name": "prune_heads", "type": null}}, "pruned_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention.pruned_heads", "name": "pruned_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "self": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention.self", "name": "self", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.tapas.configuration_tapas.TapasConfig", "kind": "Gdef", "module_public": false}, "TapasEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings", "name": "TapasEmbe<PERSON>s", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasEmbeddings", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "LayerNorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.LayerNorm", "name": "LayerNorm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.__init__", "name": "__init__", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "token_type_ids", "position_ids", "inputs_embeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.forward", "name": "forward", "type": null}}, "number_of_token_type_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.number_of_token_type_embeddings", "name": "number_of_token_type_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "position_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.position_embeddings", "name": "position_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "word_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.word_embeddings", "name": "word_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasEncoder", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasEncoder.__init__", "name": "__init__", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEncoder.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_values", "use_cache", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasEncoder.forward", "name": "forward", "type": null}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasEncoder.layer", "name": "layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasForMaskedLM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.tapas.modeling_tapas.TapasPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM", "name": "TapasForMaskedLM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasForMaskedLM", "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.__init__", "name": "__init__", "type": null}}, "_tied_weights_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM._tied_weights_keys", "name": "_tied_weights_keys", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.cls", "name": "cls", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "hidden_size", "num_hidden_layers", "num_attention_heads", "intermediate_size", "hidden_act", "hidden_dropout_prob", "attention_probs_dropout_prob", "max_position_embeddings", "type_vocab_sizes", "initializer_range", "layer_norm_eps", "pad_token_id", "positive_label_weight", "num_aggregation_labels", "aggregation_loss_weight", "use_answer_as_supervision", "answer_loss_importance", "use_normalized_answer_loss", "huber_loss_delta", "temperature", "aggregation_temperature", "use_gumbel_for_cells", "use_gumbel_for_aggregation", "average_approximation_function", "cell_selection_preference", "answer_loss_cutoff", "max_num_rows", "max_num_columns", "average_logits_per_cell", "select_one_column", "allow_empty_column_selection", "init_cell_selection_weights_to_zero", "reset_position_index_per_cell", "disable_per_token_loss", "aggregation_labels", "no_aggregation_label_index", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.tapas.configuration_tapas.TapasConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.tapas.configuration_tapas.TapasConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_ids", "attention_mask", "token_type_ids", "position_ids", "head_mask", "inputs_embeds", "encoder_hidden_states", "encoder_attention_mask", "labels", "output_attentions", "output_hidden_states", "return_dict", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_ids", "attention_mask", "token_type_ids", "position_ids", "head_mask", "inputs_embeds", "encoder_hidden_states", "encoder_attention_mask", "labels", "output_attentions", "output_hidden_states", "return_dict", "kwargs"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasForMaskedLM", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasForMaskedLM", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.MaskedLMOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.get_output_embeddings", "name": "get_output_embeddings", "type": null}}, "set_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.set_output_embeddings", "name": "set_output_embeddings", "type": null}}, "tapas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.tapas", "name": "tapas", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasForMaskedLM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasForQuestionAnswering": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.tapas.modeling_tapas.TapasPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering", "name": "TapasForQuestionAnswering", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering", "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering", "transformers.models.tapas.configuration_tapas.TapasConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TapasForQuestionAnswering", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aggregation_classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.aggregation_classifier", "name": "aggregation_classifier", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "column_output_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.column_output_bias", "name": "column_output_bias", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "column_output_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.column_output_weights", "name": "column_output_weights", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.dropout", "name": "dropout", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "token_type_ids", "position_ids", "head_mask", "inputs_embeds", "table_mask", "labels", "aggregation_labels", "float_answer", "numeric_values", "numeric_values_scale", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "token_type_ids", "position_ids", "head_mask", "inputs_embeds", "table_mask", "labels", "aggregation_labels", "float_answer", "numeric_values", "numeric_values_scale", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasForQuestionAnswering", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.models.tapas.modeling_tapas.TableQuestionAnsweringOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "output_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.output_bias", "name": "output_bias", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "output_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.output_weights", "name": "output_weights", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "tapas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.tapas", "name": "tapas", "setter_type": null, "type": "transformers.models.tapas.modeling_tapas.TapasModel"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasForQuestionAnswering", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasForSequenceClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.tapas.modeling_tapas.TapasPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification", "name": "TapasForSequenceClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasForSequenceClassification", "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification.__init__", "name": "__init__", "type": null}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "token_type_ids", "position_ids", "head_mask", "inputs_embeds", "labels", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "token_type_ids", "position_ids", "head_mask", "inputs_embeds", "labels", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasForSequenceClassification", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasForSequenceClassification", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "transformers.modeling_outputs.SequenceClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification.num_labels", "name": "num_labels", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tapas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification.tapas", "name": "tapas", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasForSequenceClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasIntermediate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasIntermediate", "name": "TapasIntermediate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasIntermediate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasIntermediate", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasIntermediate.__init__", "name": "__init__", "type": null}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasIntermediate.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasIntermediate.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasIntermediate", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasIntermediate", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "intermediate_act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasIntermediate.intermediate_act_fn", "name": "intermediate_act_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasIntermediate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasIntermediate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasLMPredictionHead": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead", "name": "TapasLMPredictionHead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasLMPredictionHead", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead.__init__", "name": "__init__", "type": null}}, "_tie_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead._tie_weights", "name": "_tie_weights", "type": null}}, "bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead.bias", "name": "bias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead.decoder", "name": "decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead.forward", "name": "forward", "type": null}}, "transform": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead.transform", "name": "transform", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasLMPredictionHead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.__init__", "name": "__init__", "type": null}}, "add_cross_attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.add_cross_attention", "name": "add_cross_attention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.attention", "name": "attention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "chunk_size_feed_forward": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.chunk_size_feed_forward", "name": "chunk_size_feed_forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "crossattention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.crossattention", "name": "crossattention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feed_forward_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attention_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.feed_forward_chunk", "name": "feed_forward_chunk", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasLayer", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "intermediate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.intermediate", "name": "intermediate", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.is_decoder", "name": "is_decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.output", "name": "output", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "seq_len_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.seq_len_dim", "name": "seq_len_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.tapas.modeling_tapas.TapasPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasModel", "name": "TapasModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasModel", "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "add_pooling_layer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.__init__", "name": "__init__", "type": null}}, "_prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads_to_prune"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel._prune_heads", "name": "_prune_heads", "type": null}}, "embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "token_type_ids", "position_ids", "head_mask", "inputs_embeds", "encoder_hidden_states", "encoder_attention_mask", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "token_type_ids", "position_ids", "head_mask", "inputs_embeds", "encoder_hidden_states", "encoder_attention_mask", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasModel", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.BaseModelOutputWithPooling"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "pooler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.pooler", "name": "pooler", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.set_input_embeddings", "name": "set_input_embeddings", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasOnlyMLMHead": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead", "name": "TapasOnlyMLMHead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sequence_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sequence_output"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasOnlyMLMHead", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "predictions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead.predictions", "name": "predictions", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasOnlyMLMHead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasOutput", "name": "TapasOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasOutput", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "LayerNorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasOutput.LayerNorm", "name": "LayerNorm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasOutput.__init__", "name": "__init__", "type": null}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasOutput.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasOutput.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "input_tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasOutput.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "input_tensor"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasOutput", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasOutput", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasPooler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasPooler", "name": "Ta<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasPooler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasPooler", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasPooler.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPooler.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPooler.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPooler.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasPooler", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasPooler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasPooler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel", "name": "TapasPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel._init_weights", "name": "_init_weights", "type": null}}, "_supports_param_buffer_assignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel._supports_param_buffer_assignment", "name": "_supports_param_buffer_assignment", "setter_type": null, "type": "builtins.bool"}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "hidden_size", "num_hidden_layers", "num_attention_heads", "intermediate_size", "hidden_act", "hidden_dropout_prob", "attention_probs_dropout_prob", "max_position_embeddings", "type_vocab_sizes", "initializer_range", "layer_norm_eps", "pad_token_id", "positive_label_weight", "num_aggregation_labels", "aggregation_loss_weight", "use_answer_as_supervision", "answer_loss_importance", "use_normalized_answer_loss", "huber_loss_delta", "temperature", "aggregation_temperature", "use_gumbel_for_cells", "use_gumbel_for_aggregation", "average_approximation_function", "cell_selection_preference", "answer_loss_cutoff", "max_num_rows", "max_num_columns", "average_logits_per_cell", "select_one_column", "allow_empty_column_selection", "init_cell_selection_weights_to_zero", "reset_position_index_per_cell", "disable_per_token_loss", "aggregation_labels", "no_aggregation_label_index", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.tapas.configuration_tapas.TapasConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.tapas.configuration_tapas.TapasConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel.supports_gradient_checkpointing", "name": "supports_gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasPredictionHeadTransform": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform", "name": "TapasPredictionHeadTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "LayerNorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform.LayerNorm", "name": "LayerNorm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform.__init__", "name": "__init__", "type": null}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasPredictionHeadTransform", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform_act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform.transform_act_fn", "name": "transform_act_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasPredictionHeadTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention", "name": "TapasSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasSelfAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.__init__", "name": "__init__", "type": null}}, "all_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.all_head_size", "name": "all_head_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "attention_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.attention_head_size", "name": "attention_head_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.forward", "name": "forward", "type": null}}, "is_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.is_decoder", "name": "is_decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.key", "name": "key", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.num_attention_heads", "name": "num_attention_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.query", "name": "query", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "transpose_for_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.transpose_for_scores", "name": "transpose_for_scores", "type": null}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.value", "name": "value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TapasSelfOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfOutput", "name": "TapasSelfOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.tapas.modeling_tapas", "mro": ["transformers.models.tapas.modeling_tapas.TapasSelfOutput", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "LayerNorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfOutput.LayerNorm", "name": "LayerNorm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfOutput.__init__", "name": "__init__", "type": null}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfOutput.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfOutput.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "input_tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfOutput.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "input_tensor"], "arg_types": ["transformers.models.tapas.modeling_tapas.TapasSelfOutput", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TapasSelfOutput", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.tapas.modeling_tapas.TapasSelfOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.tapas.modeling_tapas.TapasSelfOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.tapas.modeling_tapas.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.tapas.modeling_tapas.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.tapas.modeling_tapas.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.tapas.modeling_tapas.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.tapas.modeling_tapas.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.tapas.modeling_tapas.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_calculate_aggregate_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["answer", "pooled_output", "cell_selection_preference", "labels", "aggregation_classifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas._calculate_aggregate_mask", "name": "_calculate_aggregate_mask", "type": null}}, "_calculate_aggregation_loss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["logits_aggregation", "aggregate_mask", "aggregation_labels", "use_answer_as_supervision", "num_aggregation_labels", "aggregation_loss_weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas._calculate_aggregation_loss", "name": "_calculate_aggregation_loss", "type": null}}, "_calculate_aggregation_loss_known": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["logits_aggregation", "aggregate_mask", "aggregation_labels", "use_answer_as_supervision", "num_aggregation_labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas._calculate_aggregation_loss_known", "name": "_calculate_aggregation_loss_known", "type": null}}, "_calculate_aggregation_loss_unknown": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["logits_aggregation", "aggregate_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas._calculate_aggregation_loss_unknown", "name": "_calculate_aggregation_loss_unknown", "type": null}}, "_calculate_expected_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["dist_per_cell", "numeric_values", "numeric_values_scale", "input_mask_float", "logits_aggregation", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas._calculate_expected_result", "name": "_calculate_expected_result", "type": null}}, "_calculate_regression_loss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["answer", "aggregate_mask", "dist_per_cell", "numeric_values", "numeric_values_scale", "input_mask_float", "logits_aggregation", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas._calculate_regression_loss", "name": "_calculate_regression_loss", "type": null}}, "_segment_reduce": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["values", "index", "segment_reduce_fn", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas._segment_reduce", "name": "_segment_reduce", "type": null}}, "_single_column_cell_selection_loss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["token_logits", "column_logits", "labels", "cell_index", "col_index", "cell_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas._single_column_cell_selection_loss", "name": "_single_column_cell_selection_loss", "type": null}}, "apply_chunking_to_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.apply_chunking_to_forward", "kind": "Gdef", "module_public": false}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}, "compute_column_logits": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["sequence_output", "column_output_weights", "column_output_bias", "cell_index", "cell_mask", "allow_empty_column_selection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.compute_column_logits", "name": "compute_column_logits", "type": null}}, "compute_token_logits": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["sequence_output", "temperature", "output_weights", "output_bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.compute_token_logits", "name": "compute_token_logits", "type": null}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_public": false}, "find_pruneable_heads_and_indices": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.find_pruneable_heads_and_indices", "kind": "Gdef", "module_public": false}, "flatten": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["index", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.flatten", "name": "flatten", "type": null}}, "gather": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["values", "index", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.gather", "name": "gather", "type": null}}, "huber_loss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["input", "target", "delta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.huber_loss", "name": "huber_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["input", "target", "delta"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "huber_loss", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_tf_weights_in_tapas": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "config", "tf_checkpoint_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.load_tf_weights_in_tapas", "name": "load_tf_weights_in_tapas", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.tapas.modeling_tapas.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "prune_linear_layer": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.prune_linear_layer", "kind": "Gdef", "module_public": false}, "range_index_map": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["batch_shape", "num_segments", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.range_index_map", "name": "range_index_map", "type": null}}, "reduce_max": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["values", "index", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.reduce_max", "name": "reduce_max", "type": null}}, "reduce_mean": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["values", "index", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.reduce_mean", "name": "reduce_mean", "type": null}}, "reduce_min": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["values", "index", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.reduce_min", "name": "reduce_min", "type": null}}, "reduce_sum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["values", "index", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.tapas.modeling_tapas.reduce_sum", "name": "reduce_sum", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\tapas\\modeling_tapas.py"}