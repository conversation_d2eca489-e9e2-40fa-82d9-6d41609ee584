{"data_mtime": 1749061347, "dep_lines": [36, 50, 1208, 30, 32, 33, 34, 35, 37, 43, 50, 1208, 18, 19, 20, 21, 22, 23, 24, 25, 28, 29, 1208, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 10, 5, 10, 5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.chat_template_utils", "transformers.utils.logging", "transformers.models.auto", "huggingface_hub.errors", "transformers.audio_utils", "transformers.dynamic_module_utils", "transformers.feature_extraction_utils", "transformers.image_utils", "transformers.video_utils", "transformers.tokenization_utils_base", "transformers.utils", "transformers.models", "copy", "inspect", "json", "os", "sys", "typing", "warnings", "pathlib", "numpy", "typing_extensions", "transformers", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "_typeshed", "abc", "collections", "enum", "functools", "logging", "torch", "torch._C", "torch._tensor", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "11635988c925b20f22d351c49f49fa9a4a64200b", "id": "transformers.processing_utils", "ignore_all": true, "interface_hash": "c940c23736db92e4a6ae35d1e30479551e13bc3d", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\processing_utils.py", "plugin_data": null, "size": 83871, "suppressed": [], "version_id": "1.16.0"}