{"data_mtime": 1749061362, "dep_lines": [4, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.pipelines.base", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc", "torch", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.generation", "transformers.generation.tf_utils", "transformers.image_processing_base", "transformers.image_processing_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modelcard", "transformers.modeling_tf_utils", "transformers.modeling_utils", "transformers.processing_utils", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils.doc", "transformers.utils.hub"], "hash": "bac2e2ea41940e83ad4222ca1fde046b1d4f3846", "id": "transformers.pipelines.feature_extraction", "ignore_all": true, "interface_hash": "6df8282749c145df139eb337fef93fbb1e1bc28a", "mtime": 1749058954, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\pipelines\\feature_extraction.py", "plugin_data": null, "size": 3374, "suppressed": [], "version_id": "1.16.0"}