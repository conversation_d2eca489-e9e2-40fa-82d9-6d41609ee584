{"data_mtime": 1749061363, "dep_lines": [21, 25, 26, 21, 15, 16, 18, 19, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.pipelines.audio_classification", "transformers.pipelines.base", "transformers.utils", "collections", "typing", "numpy", "requests", "builtins", "_frozen_importlib", "abc", "logging", "transformers.utils.doc", "transformers.utils.hub"], "hash": "c67660caa6b9981dc9fced01cd3f39d4d17d3041", "id": "transformers.pipelines.zero_shot_audio_classification", "ignore_all": true, "interface_hash": "5e9ea58ec30db702d9548e1c5fc27bf8a36e948e", "mtime": 1749058954, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\pipelines\\zero_shot_audio_classification.py", "plugin_data": null, "size": 6869, "suppressed": [], "version_id": "1.16.0"}