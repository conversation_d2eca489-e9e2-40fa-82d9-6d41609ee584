{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint._async_process_executor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Future": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Future", "kind": "Gdef"}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.Metadata", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "STATE_DICT_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE", "kind": "Gdef"}, "SavePlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlanner", "kind": "Gdef"}, "StorageWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.StorageWriter", "kind": "Gdef"}, "ThreadPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.thread.ThreadPoolExecutor", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_AsyncCheckpointExecutor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._async_executor._AsyncCheckpointExecutor", "kind": "Gdef"}, "_AsyncCheckpointProcess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess", "name": "_AsyncCheckpointProcess", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.checkpoint._async_process_executor", "mro": ["torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of _AsyncCheckpointProcess", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pg_init_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pg_init_info"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess", "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AsyncCheckpointProcess", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_checkpointing_subprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["pg_init_info", "recv", "send"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess._checkpointing_subprocess", "name": "_checkpointing_subprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["pg_init_info", "recv", "send"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_checkpointing_subprocess of _AsyncCheckpointProcess", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess._checkpointing_subprocess", "name": "_checkpointing_subprocess", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["pg_init_info", "recv", "send"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_checkpointing_subprocess of _AsyncCheckpointProcess", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_execute_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["state_dict", "checkpoint_request_id", "storage_writer", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess._execute_save", "name": "_execute_save", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["state_dict", "checkpoint_request_id", "storage_writer", "planner"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_save of _AsyncCheckpointProcess", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess._execute_save", "name": "_execute_save", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["state_dict", "checkpoint_request_id", "storage_writer", "planner"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_save of _AsyncCheckpointProcess", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_mp_queue_recv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess._mp_queue_recv", "name": "_mp_queue_recv", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}}}, "_mp_queue_send": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess._mp_queue_send", "name": "_mp_queue_send", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}}}, "_save_process": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess._save_process", "name": "_save_process", "setter_type": null, "type": "multiprocessing.context.SpawnProcess"}}, "_wait_for_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess._wait_for_response", "name": "_wait_for_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wait_for_response of _AsyncCheckpointProcess", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess.ctx", "name": "ctx", "setter_type": null, "type": "multiprocessing.context.SpawnContext"}}, "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "staged_state_dict", "checkpoint_id", "storage_writer", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "staged_state_dict", "checkpoint_id", "storage_writer", "planner"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save of _AsyncCheckpointProcess", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AsyncCheckpointRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest", "name": "_AsyncCheckpointRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "staged_state_dict", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "checkpoint_request_id", "type": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "storage_writer", "type": {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "planner", "type": {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.distributed.checkpoint._async_process_executor", "mro": ["torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "staged_state_dict", "checkpoint_request_id", "storage_writer", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "staged_state_dict", "checkpoint_request_id", "storage_writer", "planner"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AsyncCheckpointRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "staged_state_dict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "checkpoint_request_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "storage_writer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "planner"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["staged_state_dict", "checkpoint_request_id", "storage_writer", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["staged_state_dict", "checkpoint_request_id", "storage_writer", "planner"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _AsyncCheckpointRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["staged_state_dict", "checkpoint_request_id", "storage_writer", "planner"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _AsyncCheckpointRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "checkpoint_request_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.checkpoint_request_id", "name": "checkpoint_request_id", "setter_type": null, "type": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier"}}, "planner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.planner", "name": "planner", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "staged_state_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.staged_state_dict", "name": "staged_state_dict", "setter_type": null, "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}}}, "storage_writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.storage_writer", "name": "storage_writer", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint._async_process_executor._AsyncCheckpointRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CHECKPOINT_PROCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._async_process_executor._CHECKPOINT_PROCESS", "name": "_CHECKPOINT_PROCESS", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.checkpoint._async_process_executor._AsyncCheckpointProcess", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_CheckpointRequestIdentifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", "name": "_CheckpointRequestIdentifier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "checkpoint_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "uuid", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.distributed.checkpoint._async_process_executor", "mro": ["torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_id"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _CheckpointRequestIdentifier", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "checkpoint_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["checkpoint_id", "uuid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["checkpoint_id", "uuid"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _CheckpointRequestIdentifier", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["checkpoint_id", "uuid"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _CheckpointRequestIdentifier", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "checkpoint_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier.checkpoint_id", "name": "checkpoint_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "uuid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier.uuid", "name": "uuid", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint._async_process_executor._CheckpointRequestIdentifier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CheckpointSaveProcessControlOpts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointSaveProcessControlOpts", "name": "_CheckpointSaveProcessControlOpts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointSaveProcessControlOpts", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch.distributed.checkpoint._async_process_executor", "mro": ["torch.distributed.checkpoint._async_process_executor._CheckpointSaveProcessControlOpts", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "INIT_COMPLETE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointSaveProcessControlOpts.INIT_COMPLETE", "name": "INIT_COMPLETE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "init_complete"}, "type_ref": "builtins.str"}}}, "TERMINATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointSaveProcessControlOpts.TERMINATE", "name": "TERMINATE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "terminate"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._async_process_executor._CheckpointSaveProcessControlOpts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint._async_process_executor._CheckpointSaveProcessControlOpts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DistWrapper": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._DistWrapper", "kind": "Gdef"}, "_ProcessBasedAsyncCheckpointExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint._async_executor._AsyncCheckpointExecutor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor", "name": "_ProcessBasedAsyncCheckpointExecutor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.checkpoint._async_process_executor", "mro": ["torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor", "torch.distributed.checkpoint._async_executor._AsyncCheckpointExecutor", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ProcessBasedAsyncCheckpointExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_execute_save_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 5, 5, 5, 5], "arg_names": ["pg_init_info", "staged_state_dict", "checkpoint_id", "storage_writer", "planner", "process_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor._execute_save_impl", "name": "_execute_save_impl", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5], "arg_names": ["pg_init_info", "staged_state_dict", "checkpoint_id", "storage_writer", "planner", "process_group"], "arg_types": [{".class": "UnionType", "items": ["torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_save_impl of _ProcessBasedAsyncCheckpointExecutor", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor._execute_save_impl", "name": "_execute_save_impl", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5], "arg_names": ["pg_init_info", "staged_state_dict", "checkpoint_id", "storage_writer", "planner", "process_group"], "arg_types": [{".class": "UnionType", "items": ["torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_save_impl of _ProcessBasedAsyncCheckpointExecutor", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor._executor", "name": "_executor", "setter_type": null, "type": "concurrent.futures.thread.ThreadPoolExecutor"}}, "execute_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "staged_state_dict", "checkpoint_id", "storage_writer", "planner", "process_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor.execute_save", "name": "execute_save", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "staged_state_dict", "checkpoint_id", "storage_writer", "planner", "process_group"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_save of _ProcessBasedAsyncCheckpointExecutor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProcessGroupInitInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", "name": "_ProcessGroupInitInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "local_rank", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "global_rank", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "world_size", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "tcp_store_master_addr", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "tcp_store_master_port", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "torch.distributed.checkpoint._async_process_executor", "mro": ["torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "process_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "process_group"], "arg_types": ["torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ProcessGroupInitInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "local_rank"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "global_rank"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "world_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tcp_store_master_addr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tcp_store_master_port"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["local_rank", "global_rank", "world_size", "tcp_store_master_addr", "tcp_store_master_port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["local_rank", "global_rank", "world_size", "tcp_store_master_addr", "tcp_store_master_port"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _ProcessGroupInitInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["local_rank", "global_rank", "world_size", "tcp_store_master_addr", "tcp_store_master_port"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _ProcessGroupInitInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "global_rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.global_rank", "name": "global_rank", "setter_type": null, "type": "builtins.int"}}, "local_rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.local_rank", "name": "local_rank", "setter_type": null, "type": "builtins.int"}}, "tcp_store_master_addr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.tcp_store_master_addr", "name": "tcp_store_master_addr", "setter_type": null, "type": "builtins.str"}}, "tcp_store_master_port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.tcp_store_master_port", "name": "tcp_store_master_port", "setter_type": null, "type": "builtins.int"}}, "world_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.world_size", "name": "world_size", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint._async_process_executor._ProcessGroupInitInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._async_process_executor.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_dcp_method_logger": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.logger._dcp_method_logger", "kind": "Gdef"}, "_get_fq_hostname": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api._get_fq_hostname", "kind": "Gdef"}, "_init_logger": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.logger._init_logger", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "get_free_port": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.utils.distributed.get_free_port", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._async_process_executor.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mp": {".class": "SymbolTableNode", "cross_ref": "torch.multiprocessing", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "uuid4": {".class": "SymbolTableNode", "cross_ref": "uuid.uuid4", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_async_process_executor.py"}