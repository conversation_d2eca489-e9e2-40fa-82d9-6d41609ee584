{"data_mtime": 1749061366, "dep_lines": [17, 9, 8, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.tensor.placement_types", "torch.distributed.tensor", "torch.nn", "abc", "functools", "typing", "torch", "builtins", "_frozen_importlib", "torch._C", "torch._tensor", "torch.distributed.device_mesh", "torch.distributed.tensor._api", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.sparse", "types"], "hash": "8b76b04f3d21adc95d6fe33db799e98b34ba0b14", "id": "torch.distributed.tensor.parallel.style", "ignore_all": true, "interface_hash": "ae9adc543838f9344c329f7d34e8f155c338ab21", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\style.py", "plugin_data": null, "size": 29904, "suppressed": [], "version_id": "1.16.0"}