{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint.state_dict_saver", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncCheckpointerType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType", "name": "AsyncCheckpointerType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch.distributed.checkpoint.state_dict_saver", "mro": ["torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "PROCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType.PROCESS", "name": "PROCESS", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "process"}, "type_ref": "builtins.str"}}}, "THREAD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType.THREAD", "name": "THREAD", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "thread"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncStager": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.staging.AsyncStager", "kind": "Gdef", "module_public": false}, "DefaultSavePlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "kind": "Gdef", "module_public": false}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "Future": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Future", "kind": "Gdef", "module_public": false}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.Metadata", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "STATE_DICT_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE", "kind": "Gdef", "module_public": false}, "SavePlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlan", "kind": "Gdef", "module_public": false}, "SavePlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlanner", "kind": "Gdef", "module_public": false}, "Stateful": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.stateful.Stateful", "kind": "Gdef", "module_public": false}, "StorageWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.StorageWriter", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_AsyncCheckpointExecutor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._async_executor._AsyncCheckpointExecutor", "kind": "Gdef", "module_public": false}, "_DistWrapper": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._DistWrapper", "kind": "Gdef", "module_public": false}, "_ProcessBasedAsyncCheckpointExecutor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._async_process_executor._ProcessBasedAsyncCheckpointExecutor", "kind": "Gdef", "module_public": false}, "_ThreadBasedAsyncCheckpointExecutor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._async_thread_executor._ThreadBasedAsyncCheckpointExecutor", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.state_dict_saver.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.state_dict_saver.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.state_dict_saver.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.state_dict_saver.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.state_dict_saver.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.state_dict_saver.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.state_dict_saver.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_api_bc_check": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._api_bc_check", "kind": "Gdef", "module_public": false}, "_copy_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._state_dict_utils._copy_state_dict", "kind": "Gdef", "module_public": false}, "_create_cpu_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._state_dict_utils._create_cpu_state_dict", "kind": "Gdef", "module_public": false}, "_dcp_method_logger": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.logger._dcp_method_logger", "kind": "Gdef", "module_public": false}, "_get_default_group": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.distributed_c10d._get_default_group", "kind": "Gdef", "module_public": false}, "_profile": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._profile", "kind": "Gdef", "module_public": false}, "_save_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["state_dict", "storage_writer", "process_group", "coordinator_rank", "no_dist", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.state_dict_saver._save_state_dict", "name": "_save_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["state_dict", "storage_writer", "process_group", "coordinator_rank", "no_dist", "planner"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint.storage.StorageWriter", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_save_state_dict", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_stateful_to_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.state_dict_saver._stateful_to_state_dict", "name": "_stateful_to_state_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state_dict"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_stateful_to_state_dict", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_storage_setup": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._storage_utils._storage_setup", "kind": "Gdef", "module_public": false}, "async_save": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["state_dict", "checkpoint_id", "storage_writer", "planner", "process_group", "async_checkpointer_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.checkpoint.state_dict_saver.async_save", "name": "async_save", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["state_dict", "checkpoint_id", "storage_writer", "planner", "process_group", "async_checkpointer_type"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "async_save", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint.state_dict_saver.async_save", "name": "async_save", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["state_dict", "checkpoint_id", "storage_writer", "planner", "process_group", "async_checkpointer_type"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch.distributed.checkpoint.state_dict_saver.AsyncCheckpointerType"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "async_save", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_public": false}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef", "module_public": false}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "save": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["state_dict", "checkpoint_id", "storage_writer", "planner", "process_group", "no_dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.checkpoint.state_dict_saver.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["state_dict", "checkpoint_id", "storage_writer", "planner", "process_group", "no_dist"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint.state_dict_saver.save", "name": "save", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "save_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["state_dict", "storage_writer", "process_group", "coordinator_rank", "no_dist", "planner"], "dataclass_transform_spec": null, "deprecated": "function torch.distributed.checkpoint.state_dict_saver.save_state_dict is deprecated: `save_state_dict` is deprecated and will be removed in future versions.Please use `save` instead.", "flags": ["is_decorated"], "fullname": "torch.distributed.checkpoint.state_dict_saver.save_state_dict", "name": "save_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["state_dict", "storage_writer", "process_group", "coordinator_rank", "no_dist", "planner"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint.storage.StorageWriter", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_state_dict", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint.state_dict_saver.save_state_dict", "name": "save_state_dict", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["state_dict", "storage_writer", "process_group", "coordinator_rank", "no_dist", "planner"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint.storage.StorageWriter", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.SavePlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_state_dict", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\state_dict_saver.py"}