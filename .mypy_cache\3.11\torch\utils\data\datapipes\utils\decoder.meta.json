{"data_mtime": 1749061368, "dep_lines": [12, 7, 190, 5, 6, 7, 8, 9, 11, 182, 190, 1, 1, 1, 1, 286, 286], "dep_prios": [5, 10, 20, 10, 10, 20, 10, 10, 10, 20, 20, 5, 30, 30, 30, 20, 20], "dependencies": ["torch.utils.data.datapipes.utils.common", "os.path", "PIL.Image", "io", "json", "os", "pickle", "tempfile", "torch", "numpy", "PIL", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "cb162b88d10dbee5482d1e17967e12dbd1870051", "id": "torch.utils.data.datapipes.utils.decoder", "ignore_all": true, "interface_hash": "68e33543cb6c3eb392dd69a1e2e736c99f1350dc", "mtime": 1749057391, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\utils\\decoder.py", "plugin_data": null, "size": 12373, "suppressed": ["scipy.io", "scipy"], "version_id": "1.16.0"}