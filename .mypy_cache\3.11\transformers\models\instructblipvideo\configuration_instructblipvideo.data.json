{".class": "MypyFile", "_fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.configuration_auto.AutoConfig", "kind": "Gdef", "module_public": false}, "CONFIG_MAPPING": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.configuration_auto.CONFIG_MAPPING", "kind": "Gdef", "module_public": false}, "InstructBlipVideoConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.configuration_utils.PretrainedConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig", "name": "InstructBlipVideoConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.instructblipvideo.configuration_instructblipvideo", "mro": ["transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vision_config", "qformer_config", "text_config", "num_query_tokens", "video_token_index", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.__init__", "name": "__init__", "type": null}}, "attribute_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.attribute_map", "name": "attribute_map", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "from_vision_qformer_text_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "vision_config", "qformer_config", "text_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.from_vision_qformer_text_configs", "name": "from_vision_qformer_text_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "vision_config", "qformer_config", "text_config", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig"}, "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig", "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig", "transformers.configuration_utils.PretrainedConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_vision_qformer_text_configs of InstructBlipVideoConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.from_vision_qformer_text_configs", "name": "from_vision_qformer_text_configs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "vision_config", "qformer_config", "text_config", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig"}, "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig", "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig", "transformers.configuration_utils.PretrainedConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_vision_qformer_text_configs of InstructBlipVideoConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "initializer_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.initializer_factor", "name": "initializer_factor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "initializer_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.initializer_range", "name": "initializer_range", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.model_type", "name": "model_type", "setter_type": null, "type": "builtins.str"}}, "num_query_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.num_query_tokens", "name": "num_query_tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "qformer_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.qformer_config", "name": "qformer_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sub_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.sub_configs", "name": "sub_configs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "transformers.configuration_utils.PretrainedConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "text_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.text_config", "name": "text_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "use_decoder_only_language_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.use_decoder_only_language_model", "name": "use_decoder_only_language_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "video_token_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.video_token_index", "name": "video_token_index", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vision_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.vision_config", "name": "vision_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstructBlipVideoQFormerConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.configuration_utils.PretrainedConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig", "name": "InstructBlipVideoQFormerConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.instructblipvideo.configuration_instructblipvideo", "mro": ["transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vocab_size", "hidden_size", "num_hidden_layers", "num_attention_heads", "intermediate_size", "hidden_act", "hidden_dropout_prob", "attention_probs_dropout_prob", "max_position_embeddings", "initializer_range", "layer_norm_eps", "pad_token_id", "position_embedding_type", "cross_attention_frequency", "encoder_hidden_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.__init__", "name": "__init__", "type": null}}, "attention_probs_dropout_prob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.attention_probs_dropout_prob", "name": "attention_probs_dropout_prob", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "base_config_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.base_config_key", "name": "base_config_key", "setter_type": null, "type": "builtins.str"}}, "cross_attention_frequency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.cross_attention_frequency", "name": "cross_attention_frequency", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder_hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.encoder_hidden_size", "name": "encoder_hidden_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hidden_act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.hidden_act", "name": "hidden_act", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hidden_dropout_prob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.hidden_dropout_prob", "name": "hidden_dropout_prob", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.hidden_size", "name": "hidden_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "initializer_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.initializer_range", "name": "initializer_range", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.intermediate_size", "name": "intermediate_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm_eps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.layer_norm_eps", "name": "layer_norm_eps", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "max_position_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.max_position_embeddings", "name": "max_position_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.model_type", "name": "model_type", "setter_type": null, "type": "builtins.str"}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.num_attention_heads", "name": "num_attention_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_hidden_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.num_hidden_layers", "name": "num_hidden_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "position_embedding_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.position_embedding_type", "name": "position_embedding_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.vocab_size", "name": "vocab_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoQFormerConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstructBlipVideoVisionConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.configuration_utils.PretrainedConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig", "name": "InstructBlipVideoVisionConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.instructblipvideo.configuration_instructblipvideo", "mro": ["transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "hidden_size", "intermediate_size", "num_hidden_layers", "num_attention_heads", "image_size", "patch_size", "hidden_act", "layer_norm_eps", "attention_dropout", "initializer_range", "qkv_bias", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.__init__", "name": "__init__", "type": null}}, "attention_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.attention_dropout", "name": "attention_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "base_config_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.base_config_key", "name": "base_config_key", "setter_type": null, "type": "builtins.str"}}, "hidden_act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.hidden_act", "name": "hidden_act", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.hidden_size", "name": "hidden_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "image_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.image_size", "name": "image_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "initializer_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.initializer_range", "name": "initializer_range", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.intermediate_size", "name": "intermediate_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm_eps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.layer_norm_eps", "name": "layer_norm_eps", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.model_type", "name": "model_type", "setter_type": null, "type": "builtins.str"}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.num_attention_heads", "name": "num_attention_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_hidden_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.num_hidden_layers", "name": "num_hidden_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "patch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.patch_size", "name": "patch_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "qkv_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.qkv_bias", "name": "qkv_bias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.instructblipvideo.configuration_instructblipvideo.InstructBlipVideoVisionConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MODEL_FOR_CAUSAL_LM_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.MODEL_FOR_CAUSAL_LM_MAPPING_NAMES", "kind": "Gdef", "module_public": false}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.instructblipvideo.configuration_instructblipvideo.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\instructblipvideo\\configuration_instructblipvideo.py"}