{"data_mtime": 1749061367, "dep_lines": [4, 5, 6, 7, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["torch.masked.maskedtensor.binary", "torch.masked.maskedtensor.core", "torch.masked.masked<PERSON>or.passthrough", "torch.masked.maskedtensor.reductions", "torch.masked.maskedtensor.unary", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "77b4f5ff383b0941c61bda13a16436bb5734734b", "id": "torch.masked.<PERSON><PERSON>or", "ignore_all": true, "interface_hash": "2bb1394dba7dcf4fc09200d3c87a194156430db0", "mtime": 1749057393, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\masked\\maskedtensor\\__init__.py", "plugin_data": null, "size": 367, "suppressed": [], "version_id": "1.16.0"}