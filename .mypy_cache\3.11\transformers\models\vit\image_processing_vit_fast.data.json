{".class": "MypyFile", "_fullname": "transformers.models.vit.image_processing_vit_fast", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseImageProcessorFast": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_utils_fast.BaseImageProcessorFast", "kind": "Gdef", "module_public": false}, "IMAGENET_STANDARD_MEAN": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.constants.IMAGENET_STANDARD_MEAN", "kind": "Gdef", "module_public": false}, "IMAGENET_STANDARD_STD": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.constants.IMAGENET_STANDARD_STD", "kind": "Gdef", "module_public": false}, "PILImageResampling": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.PILImageResampling", "kind": "Gdef", "module_public": false}, "ViTImageProcessorFast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.image_processing_utils_fast.BaseImageProcessorFast"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast", "name": "ViTImageProcessorFast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.vit.image_processing_vit_fast", "mro": ["transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast", "transformers.image_processing_utils_fast.BaseImageProcessorFast", "transformers.image_processing_utils.BaseImageProcessor", "transformers.image_processing_base.ImageProcessingMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "do_normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast.do_normalize", "name": "do_normalize", "setter_type": null, "type": "builtins.bool"}}, "do_rescale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast.do_rescale", "name": "do_rescale", "setter_type": null, "type": "builtins.bool"}}, "do_resize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast.do_resize", "name": "do_resize", "setter_type": null, "type": "builtins.bool"}}, "image_mean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast.image_mean", "name": "image_mean", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "image_std": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast.image_std", "name": "image_std", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "resample": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast.resample", "name": "resample", "setter_type": null, "type": "PIL.Image.Resampling"}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast.size", "name": "size", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vit.image_processing_vit_fast.ViTImageProcessorFast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vit.image_processing_vit_fast.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vit.image_processing_vit_fast.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vit.image_processing_vit_fast.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vit.image_processing_vit_fast.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vit.image_processing_vit_fast.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vit.image_processing_vit_fast.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vit.image_processing_vit_fast.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vit\\image_processing_vit_fast.py"}