{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CheckpointException": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.api.CheckpointException", "kind": "Gdef", "module_public": false}, "ENABLE_PROFILE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.utils.ENABLE_PROFILE", "name": "ENABLE_PROFILE", "setter_type": null, "type": "builtins.bool"}}, "MetadataIndex": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.MetadataIndex", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "R": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.R", "name": "R", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "STATE_DICT_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.shard.Shard", "kind": "Gdef", "module_public": false}, "ShardedTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.api.ShardedTensor", "kind": "Gdef", "module_public": false}, "Stats": {".class": "SymbolTableNode", "cross_ref": "pstats.Stats", "kind": "Gdef", "module_public": false}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WRAPPED_EXCEPTION": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.api.WRAPPED_EXCEPTION", "kind": "Gdef", "module_public": false}, "_DistWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.utils._DistWrapper", "name": "_DistWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._DistWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.checkpoint.utils", "mro": ["torch.distributed.checkpoint.utils._DistWrapper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "group", "use_dist", "coordinator_rank"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "group", "use_dist", "coordinator_rank"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _DistWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_gather": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "step", "map_fun"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.all_gather", "name": "all_gather", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "step", "map_fun"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", "builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_gather", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_gather of _DistWrapper", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_gather", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_gather", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "all_gather_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "object"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.all_gather_object", "name": "all_gather_object", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "object"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_gather_object", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_gather_object of _DistWrapper", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_gather_object", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_gather_object", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "all_reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "step", "map_fun", "reduce_fun"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.all_reduce", "name": "all_reduce", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "step", "map_fun", "reduce_fun"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", "builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_reduce", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_reduce", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.R", "id": -2, "name": "R", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_reduce", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_reduce of _DistWrapper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.R", "id": -2, "name": "R", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_reduce", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_reduce", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.R", "id": -2, "name": "R", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.all_reduce", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "broadcast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "step", "map_fun"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.broadcast", "name": "broadcast", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "step", "map_fun"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", "builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.broadcast", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast of _DistWrapper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.broadcast", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.broadcast", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "broadcast_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "object"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.broadcast_object", "name": "broadcast_object", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "object"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.broadcast_object", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "broadcast_object of _DistWrapper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.broadcast_object", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.broadcast_object", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "coordinator_rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.coordinator_rank", "name": "coordinator_rank", "setter_type": null, "type": "builtins.int"}}, "gather_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "object"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.gather_object", "name": "gather_object", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "object"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.gather_object", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gather_object of _DistWrapper", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.gather_object", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.gather_object", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "get_rank": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.get_rank", "name": "get_rank", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rank of _DistWrapper", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_world_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.get_world_size", "name": "get_world_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_world_size of _DistWrapper", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "global_coordinator_rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.global_coordinator_rank", "name": "global_coordinator_rank", "setter_type": null, "type": "builtins.int"}}, "group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.group", "name": "group", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "is_coordinator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.is_coordinator", "name": "is_coordinator", "setter_type": null, "type": "builtins.bool"}}, "rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.rank", "name": "rank", "setter_type": null, "type": "builtins.int"}}, "reduce_scatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "step", "map_fun", "reduce_fun"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.reduce_scatter", "name": "reduce_scatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "step", "map_fun", "reduce_fun"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", "builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.reduce_scatter", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.reduce_scatter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.R", "id": -2, "name": "R", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.reduce_scatter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce_scatter of _DistWrapper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.R", "id": -2, "name": "R", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.reduce_scatter", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.reduce_scatter", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.R", "id": -2, "name": "R", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.reduce_scatter", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "scatter_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "object_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.scatter_object", "name": "scatter_object", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "object_list"], "arg_types": ["torch.distributed.checkpoint.utils._DistWrapper", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.scatter_object", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scatter_object of _DistWrapper", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.scatter_object", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._DistWrapper.scatter_object", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "use_dist": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._DistWrapper.use_dist", "name": "use_dist", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils._DistWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.utils._DistWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ReaderView": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["io.IOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.utils._ReaderView", "name": "_Reader<PERSON>iew", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._ReaderView", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.checkpoint.utils", "mro": ["torch.distributed.checkpoint.utils._ReaderView", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "base_stream", "offset", "len"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._ReaderView.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "base_stream", "offset", "len"], "arg_types": ["torch.distributed.checkpoint.utils._ReaderView", "io.IOBase", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Reader<PERSON>iew", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._ReaderView.base_stream", "name": "base_stream", "setter_type": null, "type": "io.IOBase"}}, "len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._ReaderView.len", "name": "len", "setter_type": null, "type": "builtins.int"}}, "offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.utils._ReaderView.offset", "name": "offset", "setter_type": null, "type": "builtins.int"}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._ReaderView.read", "name": "read", "type": null}}, "readable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._ReaderView.readable", "name": "readable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.utils._ReaderView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "readable of _Reader<PERSON>iew", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readinto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._ReaderView.readinto", "name": "readinto", "type": null}}, "seek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._ReaderView.seek", "name": "seek", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["torch.distributed.checkpoint.utils._ReaderView", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seek of _<PERSON><PERSON>iew", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "seekable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._ReaderView.seekable", "name": "seekable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.utils._ReaderView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seekable of _Reader<PERSON>iew", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.utils._ReaderView.tell", "name": "tell", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.utils._ReaderView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tell of _<PERSON><PERSON>iew", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils._ReaderView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.utils._ReaderView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.utils.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_all_gather_keys": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["local_dict", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._all_gather_keys", "name": "_all_gather_keys", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["local_dict", "group"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_gather_keys", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_api_bc_check": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._api_bc_check", "name": "_api_bc_check", "type": null}}, "_assert_same_keys": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["state_dict", "process_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._assert_same_keys", "name": "_assert_same_keys", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["state_dict", "process_group"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assert_same_keys", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_file_view": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["file", "offset", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._create_file_view", "name": "_create_file_view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["file", "offset", "length"], "arg_types": ["io.IOBase", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_file_view", "ret_type": "io.IOBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_element_wise_add": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._element_wise_add", "name": "_element_wise_add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_element_wise_add", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_element_wise_sub": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._element_wise_sub", "name": "_element_wise_sub", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_element_wise_sub", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_shard": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tensor", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._find_shard", "name": "_find_shard", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tensor", "index"], "arg_types": ["torch.distributed._shard.sharded_tensor.api.ShardedTensor", "torch.distributed.checkpoint.metadata.MetadataIndex"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_shard", "ret_type": "torch.distributed._shard.sharded_tensor.shard.Shard", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_failure_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._get_failure_dict", "name": "_get_failure_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["results"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._get_failure_dict", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint.api.WRAPPED_EXCEPTION"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_failure_dict", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint.api.WRAPPED_EXCEPTION"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.utils.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint.utils._get_failure_dict", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_is_wrapped_exception": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.api._is_wrapped_exception", "kind": "Gdef", "module_public": false}, "_normalize_device_info": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["device_type", "device_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils._normalize_device_info", "name": "_normalize_device_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["device_type", "device_id"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_device_info", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_profile": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.checkpoint.utils._profile", "name": "_profile", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint.utils._profile", "name": "_profile", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_profile", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_wrap_exception": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.api._wrap_exception", "kind": "Gdef", "module_public": false}, "cProfile": {".class": "SymbolTableNode", "cross_ref": "cProfile", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef", "module_public": false}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef", "module_public": false}, "find_state_dict_object": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state_dict", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils.find_state_dict_object", "name": "find_state_dict_object", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state_dict", "index"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "torch.distributed.checkpoint.metadata.MetadataIndex"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_state_dict_object", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_tensor_shard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tensor", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.utils.find_tensor_shard", "name": "find_tensor_shard", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tensor", "index"], "arg_types": ["torch._tensor.Tensor", "torch.distributed.checkpoint.metadata.MetadataIndex"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_tensor_shard", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_public": false}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\utils.py"}