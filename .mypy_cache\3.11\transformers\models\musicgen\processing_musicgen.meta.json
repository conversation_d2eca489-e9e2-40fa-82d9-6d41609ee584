{"data_mtime": 1749061348, "dep_lines": [23, 24, 19, 21, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["transformers.processing_utils", "transformers.utils", "typing", "numpy", "builtins", "_frozen_importlib", "abc", "transformers.utils.hub"], "hash": "d3ba221e4aa9e9a425450923174b582ca33f216a", "id": "transformers.models.musicgen.processing_musicgen", "ignore_all": true, "interface_hash": "c6cd10b7f954f3628479cf74dd3fca660e39f683", "mtime": 1749058950, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\musicgen\\processing_musicgen.py", "plugin_data": null, "size": 5701, "suppressed": [], "version_id": "1.16.0"}