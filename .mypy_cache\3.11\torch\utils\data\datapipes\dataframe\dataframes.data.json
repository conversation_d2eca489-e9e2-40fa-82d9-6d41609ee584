{".class": "MypyFile", "_fullname": "torch.utils.data.datapipes.dataframe.dataframes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Capture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture", "name": "Capture", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__add__", "name": "__add__", "type": null}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__call__", "name": "__call__", "type": null}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__getattr__", "name": "__getattr__", "type": null}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__getitem__", "name": "__getitem__", "type": null}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "schema_df"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__init__", "name": "__init__", "type": null}}, "__mul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__mul__", "name": "__mul__", "type": null}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__setitem__", "name": "__setitem__", "type": null}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__setstate__", "name": "__setstate__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__str__", "name": "__str__", "type": null}}, "__sub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.__sub__", "name": "__sub__", "type": null}}, "_is_context_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture._is_context_empty", "name": "_is_context_empty", "type": null}}, "_ops_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture._ops_str", "name": "_ops_str", "type": null}}, "apply_ops_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dataframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.apply_ops_2", "name": "apply_ops_2", "type": null}}, "columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.columns", "name": "columns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.columns", "name": "columns", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "columns of Capture", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ctx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.ctx", "name": "ctx", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.Capture.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.Capture", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureF"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureA", "name": "CaptureA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureA", "torch.utils.data.datapipes.dataframe.dataframes.CaptureF", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureA.__str__", "name": "__str__", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureA.execute", "name": "execute", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureAdd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd", "name": "CaptureAdd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "left", "right", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd.__str__", "name": "__str__", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd.execute", "name": "execute", "type": null}}, "left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd.left", "name": "left", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd.right", "name": "right", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureAdd", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall", "name": "CaptureCall", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureCall", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "callable", "ctx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall.__str__", "name": "__str__", "type": null}}, "callable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall.callable", "name": "callable", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall.execute", "name": "execute", "type": null}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall.kwargs", "name": "kwargs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureCall", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureControl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureControl", "name": "CaptureControl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureControl", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureControl", "builtins.object"], "names": {".class": "SymbolTable", "disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureControl.disabled", "name": "disabled", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureControl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureControl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureDataFrame": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrame", "name": "CaptureDataFrame", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrame", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrame", "torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial", "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrame.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrame", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureDataFrameWithDataPipeOps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrame"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps", "name": "CaptureDataFrameWithDataPipeOps", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps", "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrame", "torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial", "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.__getattr__", "name": "__getattr__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.__iter__", "name": "__iter__", "type": null}}, "as_datapipe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.as_datapipe", "name": "as_datapipe", "type": null}}, "batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "batch_size", "drop_last", "wrapper_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.batch", "name": "batch", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "batch_size", "drop_last", "wrapper_class"], "arg_types": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch of CaptureDataFrameWithDataPipeOps", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.collate", "name": "collate", "type": null}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.filter", "name": "filter", "type": null}}, "groupby": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "group_key_fn", "buffer_size", "group_size", "guaranteed_group_size", "drop_remaining"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.groupby", "name": "groupby", "type": null}}, "raw_iterator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.raw_iterator", "name": "raw_iterator", "type": null}}, "shuffle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.shuffle", "name": "shuffle", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureF", "name": "CaptureF", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureF", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureF", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "ctx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureF.__init__", "name": "__init__", "type": null}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureF.kwargs", "name": "kwargs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureF.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureF", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureGetAttr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr", "name": "CaptureGetAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "src", "name", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr.__str__", "name": "__str__", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr.execute", "name": "execute", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr.name", "name": "name", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "src": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr.src", "name": "src", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureGetItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem", "name": "CaptureGetItem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "left", "key", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem.__str__", "name": "__str__", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem.execute", "name": "execute", "type": null}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem.key", "name": "key", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem.left", "name": "left", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureGetItem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureInitial": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial", "name": "CaptureInitial", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial", "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "schema_df"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureLikeMock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock", "name": "CaptureLikeMock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock.__init__", "name": "__init__", "type": null}}, "attribute": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock.attribute", "name": "attribute", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock.get_target", "name": "get_target", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock.name", "name": "name", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "save": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock.save", "name": "save", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureLikeMock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureMul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul", "name": "CaptureMul", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureMul", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "left", "right", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul.__str__", "name": "__str__", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul.execute", "name": "execute", "type": null}}, "left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul.left", "name": "left", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul.right", "name": "right", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureMul", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureSetItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem", "name": "CaptureSetItem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "left", "key", "value", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem.__str__", "name": "__str__", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem.execute", "name": "execute", "type": null}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem.key", "name": "key", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem.left", "name": "left", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem.value", "name": "value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSetItem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureSub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub", "name": "CaptureSub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureSub", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "left", "right", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub.__str__", "name": "__str__", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub.execute", "name": "execute", "type": null}}, "left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub.left", "name": "left", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub.right", "name": "right", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureSub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureVariable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.Capture"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable", "name": "CaptureVariable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable.__str__", "name": "__str__", "type": null}}, "apply_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dataframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable.apply_ops", "name": "apply_ops", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable.execute", "name": "execute", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable.name", "name": "name", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "names_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable.names_idx", "name": "names_idx", "setter_type": null, "type": "builtins.int"}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable.value", "name": "value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureVariableAssign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureF"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariableAssign", "name": "CaptureVariableAssign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariableAssign", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureVariableAssign", "torch.utils.data.datapipes.dataframe.dataframes.CaptureF", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariableAssign.__str__", "name": "__str__", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariableAssign.execute", "name": "execute", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariableAssign.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariableAssign", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DATAPIPES_OPS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DATAPIPES_OPS", "name": "DATAPIPES_OPS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "DFIterDataPipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.datapipe.DFIterDataPipe", "kind": "Gdef", "module_public": false}, "DataChunkDF": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.dataframe.structures.DataChunkDF", "kind": "Gdef", "module_public": false}, "DataFrameTracedOps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.datapipe.DFIterDataPipe"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps", "name": "DataFrameTracedOps", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps", "torch.utils.data.datapipes.datapipe.DFIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_datapipe", "output_var"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps.__iter__", "name": "__iter__", "type": null}}, "output_var": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps.output_var", "name": "output_var", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "source_datapipe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps.source_datapipe", "name": "source_datapipe", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracedOps", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DataFrameTracer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer", "name": "DataFrameTracer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.dataframe.dataframes", "mro": ["torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer", "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrameWithDataPipeOps", "torch.utils.data.datapipes.dataframe.dataframes.CaptureDataFrame", "torch.utils.data.datapipes.dataframe.dataframes.CaptureInitial", "torch.utils.data.datapipes.dataframe.dataframes.CaptureVariable", "torch.utils.data.datapipes.dataframe.dataframes.Capture", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "source_datapipe", "schema_df"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer.__init__", "name": "__init__", "type": null}}, "is_shardable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer.is_shardable", "name": "is_shardable", "type": null}}, "set_shuffle_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer.set_shuffle_settings", "name": "set_shuffle_settings", "type": null}}, "source_datapipe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer.source_datapipe", "name": "source_datapipe", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.dataframe.dataframes.DataFrameTracer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IterDataPipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "UNIMPLEMENTED_ATTR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.UNIMPLEMENTED_ATTR", "name": "UNIMPLEMENTED_ATTR", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "disable_capture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.disable_capture", "name": "disable_capture", "type": null}}, "functional_datapipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes._decorator.functional_datapipe", "kind": "Gdef", "module_public": false}, "get_val": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["capture"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.dataframe.dataframes.get_val", "name": "get_val", "type": null}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\dataframe\\dataframes.py"}