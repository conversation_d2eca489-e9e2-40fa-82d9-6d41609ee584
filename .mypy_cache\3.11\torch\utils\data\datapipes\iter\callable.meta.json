{"data_mtime": 1749061370, "dep_lines": [9, 11, 7, 8, 9, 10, 4, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.utils.data.datapipes.dataframe.dataframe_wrapper", "torch.utils.data.datapipes.utils.common", "torch.utils.data._utils.collate", "torch.utils.data.datapipes._decorator", "torch.utils.data.datapipes.dataframe", "torch.utils.data.datapipes.datapipe", "collections.abc", "functools", "collections", "typing", "builtins", "_frozen_importlib", "abc", "torch.utils.data._utils", "torch.utils.data.datapipes._typing", "torch.utils.data.datapipes.utils", "torch.utils.data.dataset", "types"], "hash": "7786fa4c7245e1944627523bf9b284322897d1a5", "id": "torch.utils.data.datapipes.iter.callable", "ignore_all": true, "interface_hash": "14338a6ae2b45ee7a3d2152a6f76c58440d4c0db", "mtime": 1749057391, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\callable.py", "plugin_data": null, "size": 9307, "suppressed": [], "version_id": "1.16.0"}