{".class": "MypyFile", "_fullname": "transformers.models.timesfm.configuration_timesfm", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef", "module_public": false}, "TimesFmConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.configuration_utils.PretrainedConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "name": "TimesFmConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.configuration_timesfm", "mro": ["transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "patch_length", "context_length", "horizon_length", "freq_size", "num_hidden_layers", "hidden_size", "intermediate_size", "head_dim", "num_attention_heads", "tolerance", "rms_norm_eps", "quantiles", "pad_val", "attention_dropout", "use_positional_embedding", "initializer_range", "min_timescale", "max_timescale", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "patch_length", "context_length", "horizon_length", "freq_size", "num_hidden_layers", "hidden_size", "intermediate_size", "head_dim", "num_attention_heads", "tolerance", "rms_norm_eps", "quantiles", "pad_val", "attention_dropout", "use_positional_embedding", "initializer_range", "min_timescale", "max_timescale", "kwargs"], "arg_types": ["transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.float", "builtins.float", "builtins.bool", "builtins.float", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.attention_dropout", "name": "attention_dropout", "setter_type": null, "type": "builtins.float"}}, "context_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.context_length", "name": "context_length", "setter_type": null, "type": "builtins.int"}}, "freq_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.freq_size", "name": "freq_size", "setter_type": null, "type": "builtins.int"}}, "head_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.head_dim", "name": "head_dim", "setter_type": null, "type": "builtins.int"}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.hidden_size", "name": "hidden_size", "setter_type": null, "type": "builtins.int"}}, "horizon_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.horizon_length", "name": "horizon_length", "setter_type": null, "type": "builtins.int"}}, "initializer_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.initializer_range", "name": "initializer_range", "setter_type": null, "type": "builtins.float"}}, "intermediate_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.intermediate_size", "name": "intermediate_size", "setter_type": null, "type": "builtins.int"}}, "is_encoder_decoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.is_encoder_decoder", "name": "is_encoder_decoder", "setter_type": null, "type": "builtins.bool"}}, "keys_to_ignore_at_inference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.keys_to_ignore_at_inference", "name": "keys_to_ignore_at_inference", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "max_timescale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.max_timescale", "name": "max_timescale", "setter_type": null, "type": "builtins.int"}}, "min_timescale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.min_timescale", "name": "min_timescale", "setter_type": null, "type": "builtins.int"}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.model_type", "name": "model_type", "setter_type": null, "type": "builtins.str"}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.num_attention_heads", "name": "num_attention_heads", "setter_type": null, "type": "builtins.int"}}, "num_hidden_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.num_hidden_layers", "name": "num_hidden_layers", "setter_type": null, "type": "builtins.int"}}, "pad_val": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.pad_val", "name": "pad_val", "setter_type": null, "type": "builtins.float"}}, "patch_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.patch_length", "name": "patch_length", "setter_type": null, "type": "builtins.int"}}, "quantiles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.quantiles", "name": "quantiles", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "rms_norm_eps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.rms_norm_eps", "name": "rms_norm_eps", "setter_type": null, "type": "builtins.float"}}, "tolerance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.tolerance", "name": "tolerance", "setter_type": null, "type": "builtins.float"}}, "use_positional_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.use_positional_embedding", "name": "use_positional_embedding", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.configuration_timesfm.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.configuration_timesfm.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.configuration_timesfm.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.configuration_timesfm.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.configuration_timesfm.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.configuration_timesfm.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.configuration_timesfm.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.configuration_timesfm.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\timesfm\\configuration_timesfm.py"}