# -*- coding: utf-8 -*-

# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/resource_policy_member.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import field_behavior_pb2 as google_dot_api_dot_field__behavior__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n*google/iam/v1/resource_policy_member.proto\x12\rgoogle.iam.v1\x1a\x1fgoogle/api/field_behavior.proto"e\n\x14ResourcePolicyMember\x12&\n\x19iam_policy_name_principal\x18\x01 \x01(\tB\x03\xe0\x41\x03\x12%\n\x18iam_policy_uid_principal\x18\x02 \x01(\tB\x03\xe0\x41\x03\x42\x87\x01\n\x11\x63om.google.iam.v1B\x19ResourcePolicyMemberProtoP\x01Z)cloud.google.com/go/iam/apiv1/iampb;iampb\xaa\x02\x13Google.Cloud.Iam.V1\xca\x02\x13Google\\Cloud\\Iam\\V1b\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "google.iam.v1.resource_policy_member_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\021com.google.iam.v1B\031ResourcePolicyMemberProtoP\001Z)cloud.google.com/go/iam/apiv1/iampb;iampb\252\002\023Google.Cloud.Iam.V1\312\002\023Google\\Cloud\\Iam\\V1"
    _RESOURCEPOLICYMEMBER.fields_by_name["iam_policy_name_principal"]._options = None
    _RESOURCEPOLICYMEMBER.fields_by_name[
        "iam_policy_name_principal"
    ]._serialized_options = b"\340A\003"
    _RESOURCEPOLICYMEMBER.fields_by_name["iam_policy_uid_principal"]._options = None
    _RESOURCEPOLICYMEMBER.fields_by_name[
        "iam_policy_uid_principal"
    ]._serialized_options = b"\340A\003"
    _globals["_RESOURCEPOLICYMEMBER"]._serialized_start = 94
    _globals["_RESOURCEPOLICYMEMBER"]._serialized_end = 195
# @@protoc_insertion_point(module_scope)
