{"data_mtime": 1749061352, "dep_lines": [32, 21, 30, 31, 21, 22, 24, 25, 26, 27, 28, 29, 30, 17, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 20, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.video_llava.configuration_video_llava", "torch.utils.checkpoint", "transformers.utils.logging", "transformers.models.auto", "torch.utils", "torch.nn", "transformers.activations", "transformers.generation", "transformers.modeling_flash_attention_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.processing_utils", "transformers.utils", "dataclasses", "typing", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "logging", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.models.auto.auto_factory", "transformers.models.auto.modeling_auto", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "3552ac00e4c7e13b7efd06e05188aa425b16f798", "id": "transformers.models.video_llava.modeling_video_llava", "ignore_all": true, "interface_hash": "bcd96edede4272814b7bd9d5c2d3dc8a4d38e04e", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\video_llava\\modeling_video_llava.py", "plugin_data": null, "size": 35162, "suppressed": [], "version_id": "1.16.0"}