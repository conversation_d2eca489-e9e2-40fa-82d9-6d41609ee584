{"data_mtime": 1749061351, "dep_lines": [51, 24, 29, 30, 42, 24, 25, 28, 31, 32, 41, 42, 58, 17, 18, 19, 20, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2439, 356], "dep_prios": [5, 10, 5, 5, 10, 20, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["transformers.models.wav2vec2.configuration_wav2vec2", "torch.utils.checkpoint", "transformers.integrations.deepspeed", "transformers.integrations.fsdp", "transformers.utils.logging", "torch.utils", "torch.nn", "transformers.activations", "transformers.modeling_flash_attention_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "safetensors.torch", "math", "warnings", "dataclasses", "typing", "numpy", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_warnings", "abc", "collections", "logging", "os", "safetensors", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.init", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.parameter", "torch.nn.utils", "torch.nn.utils.parametrizations", "torch.nn.utils.weight_norm", "torch.serialization", "torch.types", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "39f2c33a2950c5fd4555ca5d508521d3335ff21f", "id": "transformers.models.wav2vec2.modeling_wav2vec2", "ignore_all": true, "interface_hash": "f3e520445873603f72a102bd024a6ed6df3351e5", "mtime": 1749058954, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\wav2vec2\\modeling_wav2vec2.py", "plugin_data": null, "size": 116307, "suppressed": ["peft.tuners.lora", "deepspeed"], "version_id": "1.16.0"}