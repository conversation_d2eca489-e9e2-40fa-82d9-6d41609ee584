{"data_mtime": 1749061351, "dep_lines": [37, 20, 21, 27, 36, 37, 17, 18, 49, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 53, 55], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["transformers.utils.logging", "transformers.image_processing_utils", "transformers.image_processing_utils_fast", "transformers.image_utils", "transformers.processing_utils", "transformers.utils", "functools", "typing", "torch", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "abc", "collections", "enum", "logging", "numpy", "torch._C", "torch._tensor", "transformers.feature_extraction_utils", "transformers.image_processing_base", "transformers.utils.args_doc", "transformers.utils.constants", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "13006b6386376790fdf0bfc47bdf7e9bbec43a06", "id": "transformers.models.vitmatte.image_processing_vitmatte_fast", "ignore_all": true, "interface_hash": "252b8f46597a6bf674a34e2176b974df00f20c95", "mtime": 1749058954, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vitmatte\\image_processing_vitmatte_fast.py", "plugin_data": null, "size": 8643, "suppressed": ["torchvision.transforms.v2", "torchvision.transforms"], "version_id": "1.16.0"}