{"data_mtime": 1749061366, "dep_lines": [11, 7, 2, 3, 4, 5, 6, 8, 9, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["torch.utils._import_utils", "collections.abc", "fnmatch", "functools", "inspect", "os", "warnings", "io", "typing", "builtins", "_frozen_importlib", "_io", "abc", "types"], "hash": "a194ecbf0ae6e397ea06b33b4be9b981230bd4df", "id": "torch.utils.data.datapipes.utils.common", "ignore_all": true, "interface_hash": "8fb4e90307c4c38827a227d114c4539ab993e6e5", "mtime": 1749057391, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\utils\\common.py", "plugin_data": null, "size": 14112, "suppressed": [], "version_id": "1.16.0"}