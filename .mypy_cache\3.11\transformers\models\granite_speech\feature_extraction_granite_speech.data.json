{".class": "MypyFile", "_fullname": "transformers.models.granite_speech.feature_extraction_granite_speech", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AudioInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.AudioInput", "kind": "Gdef", "module_public": false}, "BatchFeature": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_utils.BatchFeature", "kind": "Gdef", "module_public": false}, "FeatureExtractionMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_utils.FeatureExtractionMixin", "kind": "Gdef", "module_public": false}, "GraniteSpeechFeatureExtractor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.feature_extraction_utils.FeatureExtractionMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", "name": "GraniteSpeechFeatureExtractor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.granite_speech.feature_extraction_granite_speech", "mro": ["transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", "transformers.feature_extraction_utils.FeatureExtractionMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "audios", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "audios", "device"], "arg_types": ["transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.AudioInput"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of GraniteSpeechFeatureExtractor", "ret_type": "transformers.feature_extraction_utils.BatchFeature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "sampling_rate", "n_fft", "win_length", "hop_length", "n_mels", "projector_window_size", "projector_downsample_rate", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "sampling_rate", "n_fft", "win_length", "hop_length", "n_mels", "projector_window_size", "projector_downsample_rate", "kwargs"], "arg_types": ["transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GraniteSpeechFeatureExtractor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ensure_melspec_transform_is_initialized": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor._ensure_melspec_transform_is_initialized", "name": "_ensure_melspec_transform_is_initialized", "type": null}}, "_extract_mel_spectrograms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "audio", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor._extract_mel_spectrograms", "name": "_extract_mel_spectrograms", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "audio", "device"], "arg_types": ["transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", "torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_mel_spectrograms of GraniteSpeechFeatureExtractor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_audios_and_audio_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "audios"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor._get_audios_and_audio_lengths", "name": "_get_audios_and_audio_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "audios"], "arg_types": ["transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.AudioInput"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_audios_and_audio_lengths of GraniteSpeechFeatureExtractor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_num_audio_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "audio_lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor._get_num_audio_features", "name": "_get_num_audio_features", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "audio_lengths"], "arg_types": ["transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_num_audio_features of GraniteSpeechFeatureExtractor", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "melspec": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor.melspec", "name": "<PERSON><PERSON><PERSON>", "setter_type": null, "type": {".class": "NoneType"}}}, "melspec_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor.melspec_kwargs", "name": "melspec_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "model_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor.model_input_names", "name": "model_input_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "projector_downsample_rate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor.projector_downsample_rate", "name": "projector_downsample_rate", "setter_type": null, "type": "builtins.int"}}, "projector_window_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor.projector_window_size", "name": "projector_window_size", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.granite_speech.feature_extraction_granite_speech.GraniteSpeechFeatureExtractor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef", "module_public": false}, "is_torchaudio_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torchaudio_available", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "requires_backends": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.requires_backends", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "torchaudio": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.granite_speech.feature_extraction_granite_speech.torchaudio", "name": "<PERSON><PERSON><PERSON>", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.granite_speech.feature_extraction_granite_speech.torchaudio", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\granite_speech\\feature_extraction_granite_speech.py"}