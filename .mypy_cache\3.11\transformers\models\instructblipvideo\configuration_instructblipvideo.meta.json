{"data_mtime": 1749061347, "dep_lines": [24, 25, 26, 23, 25, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.utils.logging", "transformers.models.auto", "transformers.configuration_utils", "transformers.utils", "builtins", "_frozen_importlib", "abc", "collections", "logging", "transformers.models.auto.configuration_auto", "transformers.utils.hub", "typing"], "hash": "e59c68321c14154bb887a37fdf1a424b63eae8f2", "id": "transformers.models.instructblipvideo.configuration_instructblipvideo", "ignore_all": true, "interface_hash": "cea441d0b8aaf502811650d1831764fac2c61472", "mtime": 1749058946, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\instructblipvideo\\configuration_instructblipvideo.py", "plugin_data": null, "size": 16946, "suppressed": [], "version_id": "1.16.0"}