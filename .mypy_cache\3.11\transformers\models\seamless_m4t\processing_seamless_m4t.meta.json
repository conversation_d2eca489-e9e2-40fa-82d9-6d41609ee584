{"data_mtime": 1749061348, "dep_lines": [19, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30, 30, 30], "dependencies": ["transformers.processing_utils", "builtins", "_frozen_importlib", "abc", "transformers.utils", "transformers.utils.hub", "typing"], "hash": "b9bd3de1fcd3a47103dabea57343e4c9e887f4e5", "id": "transformers.models.seamless_m4t.processing_seamless_m4t", "ignore_all": true, "interface_hash": "f4cd1f76c5ab183842c4204a715a4faecc25ce44", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\seamless_m4t\\processing_seamless_m4t.py", "plugin_data": null, "size": 5930, "suppressed": [], "version_id": "1.16.0"}