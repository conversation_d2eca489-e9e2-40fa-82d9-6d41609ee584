{"data_mtime": 1749061347, "dep_lines": [22, 21, 22, 17, 1, 1, 1, 1, 1, 1, 19], "dep_prios": [10, 5, 20, 5, 5, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.utils.logging", "transformers.tokenization_utils_fast", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc", "logging", "transformers.tokenization_utils_base", "transformers.utils.hub"], "hash": "d78167a09000414266c7cc932ac9e200dd862853", "id": "transformers.models.gpt_neox.tokenization_gpt_neox_fast", "ignore_all": true, "interface_hash": "fc0c5271c23fb5e4b563d18daa9f0f5f30d8bc56", "mtime": 1749058946, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\gpt_neox\\tokenization_gpt_neox_fast.py", "plugin_data": null, "size": 8999, "suppressed": ["tokenizers"], "version_id": "1.16.0"}