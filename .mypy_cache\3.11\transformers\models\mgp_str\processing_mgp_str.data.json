{".class": "MypyFile", "_fullname": "transformers.models.mgp_str.processing_mgp_str", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.tokenization_auto.AutoTokenizer", "kind": "Gdef", "module_public": false}, "DecodeType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ExplicitEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.mgp_str.processing_mgp_str.DecodeType", "name": "DecodeType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "transformers.models.mgp_str.processing_mgp_str.DecodeType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "transformers.models.mgp_str.processing_mgp_str", "mro": ["transformers.models.mgp_str.processing_mgp_str.DecodeType", "transformers.utils.generic.ExplicitEnum", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mgp_str.processing_mgp_str.DecodeType.BPE", "name": "BPE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "bpe"}, "type_ref": "builtins.str"}}}, "CHARACTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mgp_str.processing_mgp_str.DecodeType.CHARACTER", "name": "CHARACTER", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "char"}, "type_ref": "builtins.str"}}}, "WORDPIECE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mgp_str.processing_mgp_str.DecodeType.WORDPIECE", "name": "WORDPIECE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "wp"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.mgp_str.processing_mgp_str.DecodeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.mgp_str.processing_mgp_str.DecodeType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExplicitEnum": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ExplicitEnum", "kind": "Gdef", "module_public": false}, "MgpstrProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.processing_utils.ProcessorMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor", "name": "MgpstrProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.mgp_str.processing_mgp_str", "mro": ["transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor", "transformers.processing_utils.ProcessorMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "text", "images", "return_tensors", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "image_processor", "tokenizer", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.__init__", "name": "__init__", "type": null}}, "_decode_helper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pred_logits", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor._decode_helper", "name": "_decode_helper", "type": null}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.attributes", "name": "attributes", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "batch_decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sequences"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.batch_decode", "name": "batch_decode", "type": null}}, "bpe_decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sequences"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.bpe_decode", "name": "bpe_decode", "type": null}}, "bpe_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.bpe_tokenizer", "name": "bpe_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "char_decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sequences"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.char_decode", "name": "char_decode", "type": null}}, "char_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.char_tokenizer", "name": "char_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "char_tokenizer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.char_tokenizer_class", "name": "char_tokenizer_class", "setter_type": null, "type": "builtins.str"}}, "image_processor_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.image_processor_class", "name": "image_processor_class", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "wp_decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sequences"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.wp_decode", "name": "wp_decode", "type": null}}, "wp_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.wp_tokenizer", "name": "wp_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.mgp_str.processing_mgp_str.MgpstrProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProcessorMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ProcessorMixin", "kind": "Gdef", "module_public": false}, "SUPPORTED_ANNOTATION_FORMATS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.mgp_str.processing_mgp_str.SUPPORTED_ANNOTATION_FORMATS", "name": "SUPPORTED_ANNOTATION_FORMATS", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["transformers.models.mgp_str.processing_mgp_str.DecodeType", "transformers.models.mgp_str.processing_mgp_str.DecodeType", "transformers.models.mgp_str.processing_mgp_str.DecodeType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.mgp_str.processing_mgp_str.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mgp_str.processing_mgp_str.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mgp_str.processing_mgp_str.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mgp_str.processing_mgp_str.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mgp_str.processing_mgp_str.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mgp_str.processing_mgp_str.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mgp_str.processing_mgp_str.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef", "module_public": false}, "requires": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.requires", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\mgp_str\\processing_mgp_str.py"}