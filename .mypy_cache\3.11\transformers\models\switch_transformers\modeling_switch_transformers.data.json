{".class": "MypyFile", "_fullname": "transformers.models.switch_transformers.modeling_switch_transformers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2FN", "kind": "Gdef", "module_public": false}, "ALL_LAYERNORM_LAYERS": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.ALL_LAYERNORM_LAYERS", "kind": "Gdef", "module_public": false}, "AttentionMaskConverter": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_attn_mask_utils.AttentionMaskConverter", "kind": "Gdef", "module_public": false}, "BlockMask": {".class": "SymbolTableNode", "cross_ref": "torch.nn.attention.flex_attention.BlockMask", "kind": "Gdef", "module_public": false}, "Cache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.Cache", "kind": "Gdef", "module_public": false}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.CrossEntropyLoss", "kind": "Gdef", "module_public": false}, "DUMMY_INPUTS": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.DUMMY_INPUTS", "kind": "Gdef", "module_public": false}, "DUMMY_MASK": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.DUMMY_MASK", "kind": "Gdef", "module_public": false}, "DynamicCache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.DynamicCache", "kind": "Gdef", "module_public": false}, "EncoderDecoderCache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.EncoderDecoderCache", "kind": "Gdef", "module_public": false}, "GenerationMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.generation.utils.GenerationMixin", "kind": "Gdef", "module_public": false}, "MoEModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.MoEModelOutput", "kind": "Gdef", "module_public": false}, "MoEModelOutputWithPastAndCrossAttentions": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.MoEModelOutputWithPastAndCrossAttentions", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "Seq2SeqMoEModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.Seq2SeqMoEModelOutput", "kind": "Gdef", "module_public": false}, "Seq2SeqMoEOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.Seq2SeqMoEOutput", "kind": "Gdef", "module_public": false}, "SwitchTransformersAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention", "name": "SwitchTransformersAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "config", "has_relative_attention_bias", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "config", "has_relative_attention_bias", "layer_idx"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention", "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_relative_position_bucket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["relative_position", "bidirectional", "num_buckets", "max_distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention._relative_position_bucket", "name": "_relative_position_bucket", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention._relative_position_bucket", "name": "_relative_position_bucket", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["relative_position", "bidirectional", "num_buckets", "max_distance"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_relative_position_bucket of SwitchTransformersAttention", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "compute_bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "query_length", "key_length", "device", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.compute_bias", "name": "compute_bias", "type": null}}, "d_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.d_model", "name": "d_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "mask", "key_value_states", "position_bias", "past_key_value", "layer_head_mask", "query_length", "use_cache", "output_attentions", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.forward", "name": "forward", "type": null}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "has_relative_attention_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.has_relative_attention_bias", "name": "has_relative_attention_bias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "inner_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.inner_dim", "name": "inner_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "is_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.is_decoder", "name": "is_decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.k", "name": "k", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "key_value_proj_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.key_value_proj_dim", "name": "key_value_proj_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.layer_idx", "name": "layer_idx", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "n_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.n_heads", "name": "n_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "o": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.o", "name": "o", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.prune_heads", "name": "prune_heads", "type": null}}, "pruned_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.pruned_heads", "name": "pruned_heads", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "q": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.q", "name": "q", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "relative_attention_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.relative_attention_bias", "name": "relative_attention_bias", "setter_type": null, "type": "torch.nn.modules.sparse.Embedding"}}, "relative_attention_max_distance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.relative_attention_max_distance", "name": "relative_attention_max_distance", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "relative_attention_num_buckets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.relative_attention_num_buckets", "name": "relative_attention_num_buckets", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "v": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.v", "name": "v", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock", "name": "SwitchTransformersBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "config", "has_relative_attention_bias", "is_sparse", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "config", "has_relative_attention_bias", "is_sparse", "layer_idx"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "position_bias", "encoder_hidden_states", "encoder_attention_mask", "encoder_decoder_position_bias", "layer_head_mask", "cross_attn_layer_head_mask", "past_key_value", "use_cache", "output_attentions", "output_router_logits", "return_dict", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock.forward", "name": "forward", "type": null}}, "is_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock.is_decoder", "name": "is_decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "is_sparse": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock.is_sparse", "name": "is_sparse", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock.layer", "name": "layer", "setter_type": null, "type": "torch.nn.modules.container.ModuleList"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", "kind": "Gdef", "module_public": false}, "SwitchTransformersDenseActDense": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense", "name": "SwitchTransformersDenseActDense", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense", "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersDenseActDense", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense.act", "name": "act", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense.dropout", "name": "dropout", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense.forward", "name": "forward", "type": null}}, "wi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense.wi", "name": "wi", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "wo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense.wo", "name": "wo", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersEncoderModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel", "name": "SwitchTransformersEncoderModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel", "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel", "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersEncoderModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads_to_prune"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel._prune_heads", "name": "_prune_heads", "type": null}}, "_tie_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel._tie_weights", "name": "_tie_weights", "type": null}}, "_tied_weights_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel._tied_weights_keys", "name": "_tied_weights_keys", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "device_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.device_map", "name": "device_map", "setter_type": null, "type": {".class": "NoneType"}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.encoder", "name": "encoder", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "output_router_logits", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "output_router_logits", "return_dict"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of SwitchTransformersEncoderModel", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "transformers.modeling_outputs.MoEModelOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.get_encoder", "name": "get_encoder", "type": null}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.set_input_embeddings", "name": "set_input_embeddings", "type": null}}, "shared": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.shared", "name": "shared", "setter_type": null, "type": "torch.nn.modules.sparse.Embedding"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersEncoderModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersForConditionalGeneration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "transformers.generation.utils.GenerationMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration", "name": "SwitchTransformersForConditionalGeneration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration", "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "transformers.generation.utils.GenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration", "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersForConditionalGeneration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reorder_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "past_key_values", "beam_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration._reorder_cache", "name": "_reorder_cache", "type": null}}, "_tie_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration._tie_weights", "name": "_tie_weights", "type": null}}, "_tied_weights_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration._tied_weights_keys", "name": "_tied_weights_keys", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_unpack_router_logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "router_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration._unpack_router_logits", "name": "_unpack_router_logits", "type": null}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.decoder", "name": "decoder", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack"}}, "device_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.device_map", "name": "device_map", "setter_type": null, "type": {".class": "NoneType"}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.encoder", "name": "encoder", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "head_mask", "decoder_head_mask", "cross_attn_head_mask", "encoder_outputs", "past_key_values", "inputs_embeds", "decoder_inputs_embeds", "labels", "use_cache", "output_attentions", "output_hidden_states", "output_router_logits", "return_dict", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "head_mask", "decoder_head_mask", "cross_attn_head_mask", "encoder_outputs", "past_key_values", "inputs_embeds", "decoder_inputs_embeds", "labels", "use_cache", "output_attentions", "output_hidden_states", "output_router_logits", "return_dict", "cache_position"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of SwitchTransformersForConditionalGeneration", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "transformers.modeling_outputs.Seq2SeqMoEOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_decoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.get_decoder", "name": "get_decoder", "type": null}}, "get_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.get_encoder", "name": "get_encoder", "type": null}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "get_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.get_output_embeddings", "name": "get_output_embeddings", "type": null}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.lm_head", "name": "lm_head", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "model_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.model_dim", "name": "model_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prepare_decoder_input_ids_from_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.prepare_decoder_input_ids_from_labels", "name": "prepare_decoder_input_ids_from_labels", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "labels"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_decoder_input_ids_from_labels of SwitchTransformersForConditionalGeneration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "router_aux_loss_coef": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.router_aux_loss_coef", "name": "router_aux_loss_coef", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "router_z_loss_coef": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.router_z_loss_coef", "name": "router_z_loss_coef", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.set_input_embeddings", "name": "set_input_embeddings", "type": null}}, "set_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.set_output_embeddings", "name": "set_output_embeddings", "type": null}}, "shared": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.shared", "name": "shared", "setter_type": null, "type": "torch.nn.modules.sparse.Embedding"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersForConditionalGeneration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersLayerCrossAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention", "name": "SwitchTransformersLayerCrossAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "EncDecAttention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention.EncDecAttention", "name": "EncDecAttention", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "layer_idx"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersLayerCrossAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention.dropout", "name": "dropout", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "attention_mask", "position_bias", "layer_head_mask", "past_key_value", "use_cache", "query_length", "output_attentions", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention.forward", "name": "forward", "type": null}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention.layer_norm", "name": "layer_norm", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerCrossAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersLayerFF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF", "name": "SwitchTransformersLayerFF", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "is_sparse"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "is_sparse"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF", "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersLayerFF", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF.dropout", "name": "dropout", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "output_router_logits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF.forward", "name": "forward", "type": null}}, "is_sparse": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF.is_sparse", "name": "is_sparse", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF.layer_norm", "name": "layer_norm", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm"}}, "mlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF.mlp", "name": "mlp", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersDenseActDense"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerFF", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersLayerNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm", "name": "SwitchTransformersLayerNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_size", "eps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm.forward", "name": "forward", "type": null}}, "variance_epsilon": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm.variance_epsilon", "name": "variance_epsilon", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm.weight", "name": "weight", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersLayerSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention", "name": "SwitchTransformersLayerSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "SelfAttention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention.SelfAttention", "name": "SelfAttention", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersAttention"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "config", "has_relative_attention_bias", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "config", "has_relative_attention_bias", "layer_idx"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersLayerSelfAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention.dropout", "name": "dropout", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "position_bias", "layer_head_mask", "past_key_value", "use_cache", "output_attentions", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention.forward", "name": "forward", "type": null}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention.layer_norm", "name": "layer_norm", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerNorm"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersLayerSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel", "name": "SwitchTransformersModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel", "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel", "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads_to_prune"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel._prune_heads", "name": "_prune_heads", "type": null}}, "_tie_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel._tie_weights", "name": "_tie_weights", "type": null}}, "_tied_weights_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel._tied_weights_keys", "name": "_tied_weights_keys", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.decoder", "name": "decoder", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack"}}, "device_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.device_map", "name": "device_map", "setter_type": null, "type": {".class": "NoneType"}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.encoder", "name": "encoder", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "head_mask", "decoder_head_mask", "cross_attn_head_mask", "encoder_outputs", "past_key_values", "inputs_embeds", "decoder_inputs_embeds", "use_cache", "output_attentions", "output_hidden_states", "output_router_logits", "return_dict", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "head_mask", "decoder_head_mask", "cross_attn_head_mask", "encoder_outputs", "past_key_values", "inputs_embeds", "decoder_inputs_embeds", "use_cache", "output_attentions", "output_hidden_states", "output_router_logits", "return_dict", "cache_position"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of SwitchTransformersModel", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "transformers.modeling_outputs.Seq2SeqMoEModelOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_decoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.get_decoder", "name": "get_decoder", "type": null}}, "get_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.get_encoder", "name": "get_encoder", "type": null}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.set_input_embeddings", "name": "set_input_embeddings", "type": null}}, "shared": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.shared", "name": "shared", "setter_type": null, "type": "torch.nn.modules.sparse.Embedding"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "name": "SwitchTransformersPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel._init_weights", "name": "_init_weights", "type": null}}, "_no_split_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel._no_split_modules", "name": "_no_split_modules", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_shift_right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel._shift_right", "name": "_shift_right", "type": null}}, "_supports_cache_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel._supports_cache_class", "name": "_supports_cache_class", "setter_type": null, "type": "builtins.bool"}}, "_supports_static_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel._supports_static_cache", "name": "_supports_static_cache", "setter_type": null, "type": "builtins.bool"}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "d_model", "d_kv", "d_ff", "expert_capacity", "num_layers", "num_sparse_encoder_layers", "num_decoder_layers", "num_sparse_decoder_layers", "num_heads", "num_experts", "router_bias", "router_jitter_noise", "router_dtype", "router_ignore_padding_tokens", "relative_attention_num_buckets", "relative_attention_max_distance", "dropout_rate", "layer_norm_epsilon", "router_z_loss_coef", "router_aux_loss_coef", "initializer_factor", "dense_act_fn", "is_encoder_decoder", "add_router_probs", "use_cache", "pad_token_id", "eos_token_id", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dummy_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel.dummy_inputs", "name": "dummy_inputs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel.dummy_inputs", "name": "dummy_inputs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dummy_inputs of SwitchTransformersPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "supports_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel.supports_gradient_checkpointing", "name": "supports_gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersSparseMLP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP", "name": "SwitchTransformersSparseMLP", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "expert_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "expert_class"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP", "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersSparseMLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "experts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP.experts", "name": "experts", "setter_type": null, "type": "torch.nn.modules.container.ModuleDict"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP.forward", "name": "forward", "type": null}}, "router": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP.router", "name": "router", "setter_type": null, "type": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersSparseMLP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersStack": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack", "name": "SwitchTransformersStack", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack", "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "embed_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.__init__", "name": "__init__", "type": null}}, "_prepare_4d_causal_attention_mask_with_cache_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["attention_mask", "sequence_length", "target_length", "dtype", "cache_position", "batch_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack._prepare_4d_causal_attention_mask_with_cache_position", "name": "_prepare_4d_causal_attention_mask_with_cache_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["attention_mask", "sequence_length", "target_length", "dtype", "cache_position", "batch_size", "kwargs"], "arg_types": ["torch._tensor.Tensor", "builtins.int", "builtins.int", "torch._C.dtype", "torch._tensor.Tensor", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_4d_causal_attention_mask_with_cache_position of SwitchTransformersStack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack._prepare_4d_causal_attention_mask_with_cache_position", "name": "_prepare_4d_causal_attention_mask_with_cache_position", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["attention_mask", "sequence_length", "target_length", "dtype", "cache_position", "batch_size", "kwargs"], "arg_types": ["torch._tensor.Tensor", "builtins.int", "builtins.int", "torch._C.dtype", "torch._tensor.Tensor", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_4d_causal_attention_mask_with_cache_position of SwitchTransformersStack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_update_causal_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "attention_mask", "input_tensor", "cache_position", "past_key_values", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack._update_causal_mask", "name": "_update_causal_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "attention_mask", "input_tensor", "cache_position", "past_key_values", "output_attentions"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack", {".class": "UnionType", "items": ["torch._tensor.Tensor", "torch.nn.attention.flex_attention.BlockMask"], "uses_pep604_syntax": false}, "torch._tensor.Tensor", "torch._tensor.Tensor", "transformers.cache_utils.Cache", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_causal_mask of SwitchTransformersStack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.block", "name": "block", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "device_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.device_map", "name": "device_map", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "embed_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.embed_tokens", "name": "embed_tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "final_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.final_layer_norm", "name": "final_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "inputs_embeds", "head_mask", "cross_attn_head_mask", "past_key_values", "use_cache", "output_attentions", "output_hidden_states", "output_router_logits", "return_dict", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.forward", "name": "forward", "type": null}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "is_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.is_decoder", "name": "is_decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.set_input_embeddings", "name": "set_input_embeddings", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersStack", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwitchTransformersTop1Router": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router", "name": "SwitchTransformersTop1Router", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.modeling_switch_transformers", "mro": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router", "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwitchTransformersTop1Router", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cast_classifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router._cast_classifier", "name": "_cast_classifier", "type": null}}, "_compute_router_probabilities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router._compute_router_probabilities", "name": "_compute_router_probabilities", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_router_probabilities of SwitchTransformersTop1Router", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.classifier", "name": "classifier", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "expert_capacity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.expert_capacity", "name": "expert_capacity", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of SwitchTransformersTop1Router", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_padding_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.ignore_padding_tokens", "name": "ignore_padding_tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "input_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.input_dtype", "name": "input_dtype", "setter_type": null, "type": "torch._C.dtype"}}, "jitter_noise": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.jitter_noise", "name": "jitter_noise", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_experts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.num_experts", "name": "num_experts", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.modeling_switch_transformers.SwitchTransformersTop1Router", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__HEAD_MASK_WARNING_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.__HEAD_MASK_WARNING_MSG", "name": "__HEAD_MASK_WARNING_MSG", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef", "module_public": false}, "find_pruneable_heads_and_indices": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.find_pruneable_heads_and_indices", "kind": "Gdef", "module_public": false}, "is_torch_flex_attn_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_flex_attn_available", "kind": "Gdef", "module_public": false}, "is_torch_fx_proxy": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_fx_proxy", "kind": "Gdef", "module_public": false}, "is_torchdynamo_compiling": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torchdynamo_compiling", "kind": "Gdef", "module_public": false}, "load_balancing_loss_func": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["router_probs", "expert_indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.load_balancing_loss_func", "name": "load_balancing_loss_func", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["router_probs", "expert_indices"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_balancing_loss_func", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "make_flex_block_causal_mask": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.flex_attention.make_flex_block_causal_mask", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "prune_linear_layer": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.prune_linear_layer", "kind": "Gdef", "module_public": false}, "router_z_loss_func": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["router_logits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.modeling_switch_transformers.router_z_loss_func", "name": "router_z_loss_func", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["router_logits"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "router_z_loss_func", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\switch_transformers\\modeling_switch_transformers.py"}