{"data_mtime": 1749061352, "dep_lines": [26, 20, 25, 20, 21, 23, 24, 25, 16, 17, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 20, 10, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.univnet.configuration_univnet", "torch.utils.checkpoint", "transformers.utils.logging", "torch.utils", "torch.nn", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "dataclasses", "typing", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.dropout", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub"], "hash": "1721b976852ac0d569824d15bdf5393cfeb6cd2c", "id": "transformers.models.univnet.modeling_univnet", "ignore_all": true, "interface_hash": "932fff461377042855d88ffb27738ff2c5e17356", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\univnet\\modeling_univnet.py", "plugin_data": null, "size": 26036, "suppressed": [], "version_id": "1.16.0"}