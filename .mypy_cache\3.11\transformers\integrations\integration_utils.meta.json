{"data_mtime": 1749061363, "dep_lines": [39, 665, 20, 21, 35, 39, 90, 91, 92, 93, 937, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 37, 53, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 829, 1493, 1494, 1185, 1491, 1538, 2052, 1385, 1537, 1535, 1601, 2126, 2127, 64, 670, 790, 1206, 1490, 1723, 1775, 2034, 2050, 2082, 2171], "dep_prios": [10, 20, 10, 10, 10, 5, 10, 5, 5, 5, 20, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 20, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 10, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["transformers.utils.logging", "torch.utils.tensorboard", "importlib.metadata", "importlib.util", "packaging.version", "transformers.utils", "transformers.modelcard", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "transformers.trainer", "copy", "functools", "importlib", "json", "numbers", "os", "pickle", "shutil", "sys", "tempfile", "dataclasses", "enum", "pathlib", "typing", "numpy", "packaging", "transformers", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "json.decoder", "logging", "ntpath", "numpy._typing", "numpy._typing._ufunc", "torch.utils", "torch.utils.tensorboard.writer", "torch.version", "transformers.utils.generic", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "9eba78204c4ed34e159ee1a8401076cb1cffe0ff", "id": "transformers.integrations.integration_utils", "ignore_all": true, "interface_hash": "60386cbfc167db61635871d2a1cc8416b396ff77", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\integrations\\integration_utils.py", "plugin_data": null, "size": 105762, "suppressed": ["wandb.sdk.lib.config_util", "neptune.new.internal.utils", "neptune.new.metadata_containers.run", "azureml.core.run", "neptune.internal.utils", "neptune.new.exceptions", "flytekitplugins.deck.renderer", "dagshub.upload", "neptune.new", "neptune.exceptions", "neptune.utils", "dvclive.plots", "dvclive.utils", "comet_ml", "tensorboardX", "wandb", "mlflow", "neptune", "codecarbon", "clearml", "flytekit", "pandas", "dvclive", "swan<PERSON>b"], "version_id": "1.16.0"}