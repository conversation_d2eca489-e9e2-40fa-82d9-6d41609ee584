{"data_mtime": 1749061352, "dep_lines": [32, 18, 19, 31, 14, 17, 20, 21, 30, 31, 7, 8, 9, 10, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1895, 106], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["transformers.models.unispeech_sat.configuration_unispeech_sat", "transformers.integrations.deepspeed", "transformers.integrations.fsdp", "transformers.utils.logging", "torch.nn", "transformers.activations", "transformers.modeling_flash_attention_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "math", "warnings", "dataclasses", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.parameter", "torch.nn.utils", "torch.nn.utils.parametrizations", "torch.nn.utils.weight_norm", "torch.types", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "efbe17cf7240777730541d5ce87d2a4dd7a1ac5e", "id": "transformers.models.unispeech_sat.modeling_unispeech_sat", "ignore_all": true, "interface_hash": "e8249636aa428ec6249be003955cca38cc9bca6c", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\unispeech_sat\\modeling_unispeech_sat.py", "plugin_data": null, "size": 92743, "suppressed": ["peft.tuners.lora", "deepspeed"], "version_id": "1.16.0"}