{"data_mtime": 1749061343, "dep_lines": [33, 25, 26, 32, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 21, 22, 18, 20, 23, 18, 19], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10, 10, 5, 20, 10], "dependencies": ["transformers.models.vit.configuration_vit", "transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc", "collections", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "42b02de2ed480d88a90e0d0054303befda88cb5a", "id": "transformers.models.vit.modeling_flax_vit", "ignore_all": true, "interface_hash": "61c11bc11e853e4d7e936e21776d5ce55e104969", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vit\\modeling_flax_vit.py", "plugin_data": null, "size": 25510, "suppressed": ["flax.core.frozen_dict", "flax.linen.attention", "flax.linen", "jax.numpy", "flax.traverse_util", "flax", "jax"], "version_id": "1.16.0"}