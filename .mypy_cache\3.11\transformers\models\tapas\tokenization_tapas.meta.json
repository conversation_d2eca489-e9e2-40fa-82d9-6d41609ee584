{"data_mtime": 1749061346, "dep_lines": [39, 30, 31, 39, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 43], "dep_prios": [10, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.utils.logging", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils", "collections", "datetime", "enum", "itertools", "math", "os", "re", "unicodedata", "dataclasses", "typing", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "genericpath", "logging", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "typing_extensions"], "hash": "3382c9394b3ab29bc512465d003da9bc1c49c3d7", "id": "transformers.models.tapas.tokenization_tapas", "ignore_all": true, "interface_hash": "ddcfcd4f0f1a2355d72154985f0397d4bbe58a70", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\tapas\\tokenization_tapas.py", "plugin_data": null, "size": 118458, "suppressed": ["pandas"], "version_id": "1.16.0"}