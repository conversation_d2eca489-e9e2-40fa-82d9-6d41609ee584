{"data_mtime": 1749061347, "dep_lines": [28, 21, 27, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25], "dep_prios": [10, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.utils.logging", "transformers.utils", "transformers.tokenization_utils", "collections", "os", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "genericpath", "io", "logging", "ntpath", "transformers.tokenization_utils_base", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "e7ec778a443444b9b9eab9ab57dabf39e13c9425", "id": "transformers.models.cpmant.tokenization_cpmant", "ignore_all": true, "interface_hash": "dc73b0f5d788fb696159c328e204da6c761f99de", "mtime": 1749058941, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\cpmant\\tokenization_cpmant.py", "plugin_data": null, "size": 9760, "suppressed": ["ji<PERSON>a"], "version_id": "1.16.0"}