{"data_mtime": 1749061348, "dep_lines": [34, 30, 21, 29, 30, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.udop.tokenization_udop", "transformers.utils.logging", "transformers.tokenization_utils_base", "transformers.tokenization_utils_fast", "transformers.utils", "os", "shutil", "typing", "builtins", "_frozen_importlib", "abc", "collections", "enum", "logging", "transformers.tokenization_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "typing_extensions"], "hash": "c9b581703f79b20c57ba7f3bae9574c289b97ccb", "id": "transformers.models.udop.tokenization_udop_fast", "ignore_all": true, "interface_hash": "a6b095a814da0adb7e504bc027560fe6be8a016b", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\udop\\tokenization_udop_fast.py", "plugin_data": null, "size": 49828, "suppressed": [], "version_id": "1.16.0"}