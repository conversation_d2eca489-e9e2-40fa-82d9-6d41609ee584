{"data_mtime": 1749061380, "dep_lines": [26, 37, 24, 22, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.elastic.rendezvous.api", "torch.distributed.elastic.rendezvous.utils", "torch.distributed.elastic.events", "torch.distributed", "inspect", "logging", "os", "pickle", "socket", "threading", "time", "weakref", "abc", "dataclasses", "datetime", "enum", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_pickle", "_thread", "_typeshed", "torch._C", "torch._C._distributed_c10d", "torch.distributed.elastic.events.api", "types", "typing_extensions"], "hash": "0110ae1a906dd6760e05bdea915bd1a4bdaf3a93", "id": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "ignore_all": true, "interface_hash": "5bf4406be7ef8253054d800f88e4ce1eb30ea732", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\elastic\\rendezvous\\dynamic_rendezvous.py", "plugin_data": null, "size": 50877, "suppressed": [], "version_id": "1.16.0"}