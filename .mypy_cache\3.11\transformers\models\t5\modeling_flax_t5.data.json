{".class": "MypyFile", "_fullname": "transformers.models.t5.modeling_flax_t5", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.ACT2FN", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "FLAX_T5_CONDITIONAL_GENERATION_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FLAX_T5_CONDITIONAL_GENERATION_DOCSTRING", "name": "FLAX_T5_CONDITIONAL_GENERATION_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "FLAX_T5_MODEL_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FLAX_T5_MODEL_DOCSTRING", "name": "FLAX_T5_MODEL_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "FlaxBaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxBaseModelOutput", "kind": "Gdef", "module_public": false}, "FlaxBaseModelOutputWithPastAndCrossAttentions": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxBaseModelOutputWithPastAndCrossAttentions", "kind": "Gdef", "module_public": false}, "FlaxCausalLMOutputWithCrossAttentions": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxCausalLMOutputWithCrossAttentions", "kind": "Gdef", "module_public": false}, "FlaxPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "kind": "Gdef", "module_public": false}, "FlaxSeq2SeqLMOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxSeq2SeqLMOutput", "kind": "Gdef", "module_public": false}, "FlaxSeq2SeqModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxSeq2SeqModelOutput", "kind": "Gdef", "module_public": false}, "FlaxT5Attention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention", "name": "FlaxT5Attention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5Attention", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "key_value_states", "position_bias", "use_cache", "output_attentions", "deterministic", "init_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.__call__", "name": "__call__", "type": null}}, "_concatenate_to_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "key", "value", "query", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention._concatenate_to_cache", "name": "_concatenate_to_cache", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention._concatenate_to_cache", "name": "_concatenate_to_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "_create_position_bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "key_states", "query_states", "attention_mask", "init_cache", "seq_length", "causal_attention_mask_shift"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention._create_position_bias", "name": "_create_position_bias", "type": null}}, "_merge_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention._merge_heads", "name": "_merge_heads", "type": null}}, "_relative_position_bucket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["relative_position", "bidirectional", "num_buckets", "max_distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention._relative_position_bucket", "name": "_relative_position_bucket", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention._relative_position_bucket", "name": "_relative_position_bucket", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["relative_position", "bidirectional", "num_buckets", "max_distance"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_relative_position_bucket of FlaxT5Attention", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_split_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention._split_heads", "name": "_split_heads", "type": null}}, "causal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.causal", "name": "causal", "setter_type": null, "type": "builtins.bool"}}, "compute_bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query_length", "key_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.compute_bias", "name": "compute_bias", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "d_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.d_model", "name": "d_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "has_relative_attention_bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.has_relative_attention_bias", "name": "has_relative_attention_bias", "setter_type": null, "type": "builtins.bool"}}, "inner_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.inner_dim", "name": "inner_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.k", "name": "k", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "key_value_proj_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.key_value_proj_dim", "name": "key_value_proj_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "n_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.n_heads", "name": "n_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "o": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.o", "name": "o", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "q": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.q", "name": "q", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "relative_attention_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.relative_attention_bias", "name": "relative_attention_bias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "relative_attention_max_distance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.relative_attention_max_distance", "name": "relative_attention_max_distance", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "relative_attention_num_buckets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.relative_attention_num_buckets", "name": "relative_attention_num_buckets", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.setup", "name": "setup", "type": null}}, "v": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.v", "name": "v", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5Attention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5Block": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block", "name": "FlaxT5Block", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5Block", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "position_bias", "encoder_hidden_states", "encoder_attention_mask", "encoder_decoder_position_bias", "output_attentions", "return_dict", "deterministic", "init_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block.__call__", "name": "__call__", "type": null}}, "causal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block.causal", "name": "causal", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "has_relative_attention_bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block.has_relative_attention_bias", "name": "has_relative_attention_bias", "setter_type": null, "type": "builtins.bool"}}, "layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block.layer", "name": "layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Block.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5Block", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5BlockCollection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection", "name": "FlaxT5BlockCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "output_attentions", "output_hidden_states", "deterministic", "init_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "output_attentions", "output_hidden_states", "deterministic", "init_cache"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxT5BlockCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection.blocks", "name": "blocks", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "causal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection.causal", "name": "causal", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5BlockCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5DenseActDense": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense", "name": "FlaxT5DenseActDense", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.__call__", "name": "__call__", "type": null}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.act", "name": "act", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.setup", "name": "setup", "type": null}}, "wi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.wi", "name": "wi", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "wo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.wo", "name": "wo", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseActDense", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5DenseGatedActDense": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense", "name": "FlaxT5DenseGatedActDense", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.__call__", "name": "__call__", "type": null}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.act", "name": "act", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.setup", "name": "setup", "type": null}}, "wi_0": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.wi_0", "name": "wi_0", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "wi_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.wi_1", "name": "wi_1", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "wo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.wo", "name": "wo", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5DenseGatedActDense", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5EncoderModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel", "name": "FlaxT5EncoderModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel", "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel", {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.PRNGKey", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxT5EncoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel.__call__", "name": "__call__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5EncoderModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule", "name": "FlaxT5EncoderModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxT5EncoderModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule.setup", "name": "setup", "type": null}}, "shared": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule.shared", "name": "shared", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5EncoderModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5ForConditionalGeneration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration", "name": "FlaxT5ForConditionalGeneration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration", "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.PRNGKey", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of FlaxT5ForConditionalGeneration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration.decode", "name": "decode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_inputs_for_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "decoder_input_ids", "max_length", "attention_mask", "decoder_attention_mask", "encoder_outputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration.prepare_inputs_for_generation", "name": "prepare_inputs_for_generation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "decoder_input_ids", "max_length", "attention_mask", "decoder_attention_mask", "encoder_outputs", "kwargs"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jax", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jax", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_inputs_for_generation of FlaxT5ForConditionalGeneration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_inputs_for_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_outputs", "model_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration.update_inputs_for_generation", "name": "update_inputs_for_generation", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGeneration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5ForConditionalGenerationModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule", "name": "FlaxT5ForConditionalGenerationModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "encoder_outputs", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "encoder_outputs", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxT5ForConditionalGenerationModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_decoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule._get_decoder_module", "name": "_get_decoder_module", "type": null}}, "_get_encoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule._get_encoder_module", "name": "_get_encoder_module", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.decoder", "name": "decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.lm_head", "name": "lm_head", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.model_dim", "name": "model_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.setup", "name": "setup", "type": null}}, "shared": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.shared", "name": "shared", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5ForConditionalGenerationModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5LayerCollection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection", "name": "FlaxT5LayerCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "position_bias", "encoder_hidden_states", "encoder_attention_mask", "encoder_decoder_position_bias", "output_attentions", "deterministic", "init_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "has_relative_attention_bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection.has_relative_attention_bias", "name": "has_relative_attention_bias", "setter_type": null, "type": "builtins.bool"}}, "layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection.layer", "name": "layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5LayerCrossAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention", "name": "FlaxT5LayerCrossAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention", "builtins.object"], "names": {".class": "SymbolTable", "EncDecAttention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention.EncDecAttention", "name": "EncDecAttention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "attention_mask", "position_bias", "output_attentions", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerCrossAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5LayerFF": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF", "name": "FlaxT5LayerFF", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF", "builtins.object"], "names": {".class": "SymbolTable", "DenseReluDense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF.DenseReluDense", "name": "DenseReluDense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerFF", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5LayerNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm", "name": "FlaxT5LayerNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm.__call__", "name": "__call__", "type": null}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "eps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm.eps", "name": "eps", "setter_type": null, "type": "builtins.float"}}, "hidden_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm.hidden_size", "name": "hidden_size", "setter_type": null, "type": "builtins.int"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm.setup", "name": "setup", "type": null}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm.weight", "name": "weight", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm.weight_init", "name": "weight_init", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5LayerSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention", "name": "FlaxT5LayerSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention", "builtins.object"], "names": {".class": "SymbolTable", "SelfAttention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.SelfAttention", "name": "SelfAttention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "position_bias", "output_attentions", "deterministic", "init_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "has_relative_attention_bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.has_relative_attention_bias", "name": "has_relative_attention_bias", "setter_type": null, "type": "builtins.bool"}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5LayerSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5Model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Model", "name": "FlaxT5Model", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Model", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5Model", "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Model.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.t5.modeling_flax_t5.FlaxT5Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Model.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5Model", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5Module": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module", "name": "FlaxT5Module", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5Module", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "encoder_outputs", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "encoder_outputs", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5Module", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxT5Module", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_decoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module._get_decoder_module", "name": "_get_decoder_module", "type": null}}, "_get_encoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module._get_encoder_module", "name": "_get_encoder_module", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.decoder", "name": "decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.setup", "name": "setup", "type": null}}, "shared": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.shared", "name": "shared", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Module.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5Module", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5PreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_flax_utils.FlaxPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", "name": "FlaxT5PreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.PRNGKey", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxT5PreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.__call__", "name": "__call__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "gradient_checkpointing", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "gradient_checkpointing", "kwargs"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", "transformers.models.t5.configuration_t5.T5Config", {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int", {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FlaxT5PreTrainedModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "d_model", "d_kv", "d_ff", "num_layers", "num_decoder_layers", "num_heads", "relative_attention_num_buckets", "relative_attention_max_distance", "dropout_rate", "layer_norm_epsilon", "initializer_factor", "feed_forward_proj", "is_encoder_decoder", "use_cache", "pad_token_id", "eos_token_id", "classifier_dropout", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.t5.configuration_t5.T5Config"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.t5.configuration_t5.T5Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.PRNGKey", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of FlaxT5PreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.decode", "name": "decode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "enable_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.enable_gradient_checkpointing", "name": "enable_gradient_checkpointing", "type": null}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.PRNGKey", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of FlaxT5PreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.encode", "name": "encode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "init_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "batch_size", "max_length", "encoder_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.init_cache", "name": "init_cache", "type": null}}, "init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.init_weights", "name": "init_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jax", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.FrozenDict", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_weights of FlaxT5PreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.FrozenDict", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.module_class", "name": "module_class", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.nn", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5PreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxT5Stack": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack", "name": "FlaxT5Stack", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.t5.modeling_flax_t5", "mro": ["transformers.models.t5.modeling_flax_t5.FlaxT5Stack", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "output_attentions", "output_hidden_states", "return_dict", "deterministic", "init_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "output_attentions", "output_hidden_states", "return_dict", "deterministic", "init_cache"], "arg_types": ["transformers.models.t5.modeling_flax_t5.FlaxT5Stack", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxT5Stack", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.block", "name": "block", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "causal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.causal", "name": "causal", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.config", "name": "config", "setter_type": null, "type": "transformers.models.t5.configuration_t5.T5Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "embed_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.embed_tokens", "name": "embed_tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.nn", "source_any": null, "type_of_any": 3}}}, "final_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.final_layer_norm", "name": "final_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.t5.modeling_flax_t5.FlaxT5Stack", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrozenDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.FrozenDict", "name": "FrozenDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.FrozenDict", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PRNGKey": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.PRNGKey", "name": "PRNGKey", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.PRNGKey", "source_any": null, "type_of_any": 3}}}, "T5Config": {".class": "SymbolTableNode", "cross_ref": "transformers.models.t5.configuration_t5.T5Config", "kind": "Gdef", "module_public": false}, "T5_DECODE_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.T5_DECODE_INPUTS_DOCSTRING", "name": "T5_DECODE_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "T5_ENCODE_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.T5_ENCODE_INPUTS_DOCSTRING", "name": "T5_ENCODE_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "T5_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.T5_INPUTS_DOCSTRING", "name": "T5_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "T5_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.T5_START_DOCSTRING", "name": "T5_START_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "setter_type": null, "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.t5.modeling_flax_t5.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "append_call_sample_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.append_call_sample_docstring", "kind": "Gdef", "module_public": false}, "append_replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.append_replace_return_docstrings", "kind": "Gdef", "module_public": false}, "combine_masks": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.combine_masks", "name": "combine_masks", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.combine_masks", "source_any": null, "type_of_any": 3}}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef", "module_public": false}, "dot_product_attention_weights": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.dot_product_attention_weights", "name": "dot_product_attention_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.dot_product_attention_weights", "source_any": null, "type_of_any": 3}}}, "flatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.flatten_dict", "name": "flatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.flatten_dict", "source_any": null, "type_of_any": 3}}}, "freeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.freeze", "name": "freeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.freeze", "source_any": null, "type_of_any": 3}}}, "jax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.jax", "name": "jax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jax", "source_any": null, "type_of_any": 3}}}, "jnp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.jnp", "name": "jnp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "make_causal_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.make_causal_mask", "name": "make_causal_mask", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.make_causal_mask", "source_any": null, "type_of_any": 3}}}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.nn", "name": "nn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.nn", "source_any": null, "type_of_any": 3}}}, "nn_partitioning": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.nn_partitioning", "name": "nn_partitioning", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.nn_partitioning", "source_any": null, "type_of_any": 3}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "overwrite_call_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.overwrite_call_docstring", "kind": "Gdef", "module_public": false}, "remat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.t5.modeling_flax_t5.remat", "name": "remat", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.nn_partitioning", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.nn_partitioning", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "shift_tokens_right": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["input_ids", "pad_token_id", "decoder_start_token_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.t5.modeling_flax_t5.shift_tokens_right", "name": "shift_tokens_right", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["input_ids", "pad_token_id", "decoder_start_token_id"], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shift_tokens_right", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.jnp", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unflatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.unflatten_dict", "name": "unflatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.unflatten_dict", "source_any": null, "type_of_any": 3}}}, "unfreeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.t5.modeling_flax_t5.unfreeze", "name": "unfreeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.t5.modeling_flax_t5.unfreeze", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\t5\\modeling_flax_t5.py"}