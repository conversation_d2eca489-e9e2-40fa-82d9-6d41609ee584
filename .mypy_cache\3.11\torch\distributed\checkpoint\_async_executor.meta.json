{"data_mtime": 1749061368, "dep_lines": [9, 10, 11, 5, 8, 3, 4, 6, 8, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.storage", "concurrent.futures", "torch.distributed", "abc", "os", "typing", "torch", "builtins", "_frozen_importlib", "concurrent", "concurrent.futures._base", "torch._C", "torch._C._distributed_c10d"], "hash": "be97e5f2cbd8e6c87b50a1e4e1c23108a10e4ec5", "id": "torch.distributed.checkpoint._async_executor", "ignore_all": true, "interface_hash": "c0d39ae729e7aba1e1c7bdd91a1b8d1cfe7661bb", "mtime": 1749057439, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_async_executor.py", "plugin_data": null, "size": 1129, "suppressed": [], "version_id": "1.16.0"}