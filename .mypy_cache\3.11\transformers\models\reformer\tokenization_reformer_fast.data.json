{".class": "MypyFile", "_fullname": "transformers.models.reformer.tokenization_reformer_fast", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedTokenizerFast": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "kind": "Gdef", "module_public": false}, "ReformerTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.models.reformer.tokenization_reformer.ReformerTokenizer", "kind": "Gdef", "module_public": false}, "ReformerTokenizerFast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast", "name": "ReformerTokenizerFast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.reformer.tokenization_reformer_fast", "mro": ["transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast", "transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "transformers.tokenization_utils_base.SpecialTokensMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vocab_file", "tokenizer_file", "eos_token", "unk_token", "additional_special_tokens", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast.__init__", "name": "__init__", "type": null}}, "can_save_slow_tokenizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast.can_save_slow_tokenizer", "name": "can_save_slow_tokenizer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_save_slow_tokenizer of ReformerTokenizerFast", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast.can_save_slow_tokenizer", "name": "can_save_slow_tokenizer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_save_slow_tokenizer of ReformerTokenizerFast", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast.model_input_names", "name": "model_input_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "save_vocabulary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "save_directory", "filename_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast.save_vocabulary", "name": "save_vocabulary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "save_directory", "filename_prefix"], "arg_types": ["transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_vocabulary of ReformerTokenizerFast", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "slow_tokenizer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast.slow_tokenizer_class", "name": "slow_tokenizer_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["vocab_file", "eos_token", "unk_token", "additional_special_tokens", "sp_model_kwargs", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.reformer.tokenization_reformer.ReformerTokenizer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.reformer.tokenization_reformer.ReformerTokenizer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vocab_files_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast.vocab_files_names", "name": "vocab_files_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.reformer.tokenization_reformer_fast.ReformerTokenizerFast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SPIECE_UNDERLINE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.SPIECE_UNDERLINE", "name": "SPIECE_UNDERLINE", "setter_type": null, "type": "builtins.str"}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "VOCAB_FILES_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.VOCAB_FILES_NAMES", "name": "VOCAB_FILES_NAMES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "copyfile": {".class": "SymbolTableNode", "cross_ref": "shutil.copyfile", "kind": "Gdef", "module_public": false}, "is_sentencepiece_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_sentencepiece_available", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.reformer.tokenization_reformer_fast.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\reformer\\tokenization_reformer_fast.py"}