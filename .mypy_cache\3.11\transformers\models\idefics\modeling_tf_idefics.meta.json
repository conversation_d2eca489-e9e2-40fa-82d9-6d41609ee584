{"data_mtime": 1749061363, "dep_lines": [46, 47, 48, 40, 30, 31, 32, 39, 40, 22, 24, 25, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.idefics.configuration_idefics", "transformers.models.idefics.perceiver_tf", "transformers.models.idefics.vision_tf", "transformers.utils.logging", "transformers.activations_tf", "transformers.modeling_tf_outputs", "transformers.modeling_tf_utils", "transformers.tf_utils", "transformers.utils", "__future__", "dataclasses", "typing", "transformers", "builtins", "_frozen_importlib", "abc", "collections", "logging", "numpy", "transformers.configuration_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.utils.doc", "transformers.utils.dummy_tf_objects", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "6e7ebef9debca8b0ed12b91ee013958109b4df99", "id": "transformers.models.idefics.modeling_tf_idefics", "ignore_all": true, "interface_hash": "98c7245b4f424718c96b1a54683033f468ccdb78", "mtime": 1749058946, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\idefics\\modeling_tf_idefics.py", "plugin_data": null, "size": 80317, "suppressed": ["tensorflow"], "version_id": "1.16.0"}