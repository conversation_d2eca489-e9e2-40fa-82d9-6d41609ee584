{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint.optimizer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.BytesStorageMetadata", "kind": "Gdef", "module_public": false}, "ChunkShardingSpec": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharding_spec.chunk_sharding_spec.ChunkShardingSpec", "kind": "Gdef", "module_public": false}, "ChunkStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.ChunkStorageMetadata", "kind": "Gdef", "module_public": false}, "DTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.DTensor", "kind": "Gdef", "module_public": false}, "DefaultLoadPlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "kind": "Gdef", "module_public": false}, "LoadPlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlan", "kind": "Gdef", "module_public": false}, "LoadPlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlanner", "kind": "Gdef", "module_public": false}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.Metadata", "kind": "Gdef", "module_public": false}, "MetadataIndex": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.MetadataIndex", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "STATE_DICT_2D_LAYOUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.checkpoint.optimizer.STATE_DICT_2D_LAYOUT", "line": 45, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "STATE_DICT_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.shard.Shard", "kind": "Gdef", "module_public": false}, "ShardTensorProperties": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.metadata.TensorProperties", "kind": "Gdef", "module_public": false}, "ShardedTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.api.ShardedTensor", "kind": "Gdef", "module_public": false}, "StorageReader": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.StorageReader", "kind": "Gdef", "module_public": false}, "TensorProperties": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.TensorProperties", "kind": "Gdef", "module_public": false}, "TensorStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.TensorStorageMetadata", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_ReaderWithOffset": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset", "name": "_ReaderWithOffset", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.checkpoint.optimizer", "mro": ["torch.distributed.checkpoint.optimizer._ReaderWithOffset", "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.planner.LoadPlanner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fqn_to_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fqn_to_offset"], "arg_types": ["torch.distributed.checkpoint.optimizer._ReaderWithOffset", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ReaderWithOffset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_local_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset.create_local_plan", "name": "create_local_plan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.optimizer._ReaderWithOffset"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_local_plan of _ReaderWithOffset", "ret_type": "torch.distributed.checkpoint.planner.LoadPlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fqn_to_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset.fqn_to_offset", "name": "fqn_to_offset", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "lookup_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset.lookup_tensor", "name": "lookup_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["torch.distributed.checkpoint.optimizer._ReaderWithOffset", "torch.distributed.checkpoint.metadata.MetadataIndex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lookup_tensor of _ReaderWithOffset", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset.metadata", "name": "metadata", "setter_type": null, "type": "torch.distributed.checkpoint.metadata.Metadata"}}, "state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset.state_dict", "name": "state_dict", "setter_type": null, "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}}}, "translation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset.translation", "name": "translation", "setter_type": null, "type": {".class": "Instance", "args": ["torch.distributed.checkpoint.metadata.MetadataIndex", "torch.distributed.checkpoint.metadata.MetadataIndex"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.optimizer._ReaderWithOffset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.optimizer._ReaderWithOffset", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.optimizer.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.optimizer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.optimizer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.optimizer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.optimizer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.optimizer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.optimizer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_alloc_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["props", "size", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.optimizer._alloc_tensor", "name": "_alloc_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["props", "size", "device_type"], "arg_types": ["torch.distributed.checkpoint.metadata.TensorProperties", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alloc_tensor", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_chunk_sharded_tensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.fsdp._shard_utils._create_chunk_sharded_tensor", "kind": "Gdef", "module_public": false}, "_create_colwise_spec": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["pg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.optimizer._create_colwise_spec", "name": "_create_colwise_spec", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["pg"], "arg_types": [{".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_colwise_spec", "ret_type": "torch.distributed._shard.sharding_spec.chunk_sharding_spec.ChunkShardingSpec", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_read_items": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner_helpers._create_read_items", "kind": "Gdef", "module_public": false}, "_element_wise_add": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._element_wise_add", "kind": "Gdef", "module_public": false}, "_element_wise_sub": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._element_wise_sub", "kind": "Gdef", "module_public": false}, "_gen_rank_device": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["global_rank", "device_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.optimizer._gen_rank_device", "name": "_gen_rank_device", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["global_rank", "device_type"], "arg_types": ["builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gen_rank_device", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_default_group": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.distributed_c10d._get_default_group", "kind": "Gdef", "module_public": false}, "_get_device_module": {".class": "SymbolTableNode", "cross_ref": "torch._utils._get_device_module", "kind": "Gdef", "module_public": false}, "_get_state_dict_2d_layout": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.optimizer._get_state_dict_2d_layout", "name": "_get_state_dict_2d_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state_dict"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_state_dict_2d_layout", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint.optimizer.STATE_DICT_2D_LAYOUT"}, {".class": "UnionType", "items": ["torch._C._distributed_c10d.ProcessGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_nested_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.optimizer._is_nested_tensor", "name": "_is_nested_tensor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_nested_tensor", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_device_info": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils._normalize_device_info", "kind": "Gdef", "module_public": false}, "_remote_device": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.remote_device._remote_device", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "create_read_items_for_chunk_list": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner_helpers.create_read_items_for_chunk_list", "kind": "Gdef", "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef", "module_public": false}, "load_sharded_optimizer_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["model_state_dict", "optimizer_key", "storage_reader", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.optimizer.load_sharded_optimizer_state_dict", "name": "load_sharded_optimizer_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["model_state_dict", "optimizer_key", "storage_reader", "planner"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "builtins.str", "torch.distributed.checkpoint.storage.StorageReader", {".class": "UnionType", "items": ["torch.distributed.checkpoint.planner.LoadPlanner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_sharded_optimizer_state_dict", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.state_dict_loader.load_state_dict", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "unflatten_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._nested_dict.unflatten_state_dict", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\optimizer.py"}