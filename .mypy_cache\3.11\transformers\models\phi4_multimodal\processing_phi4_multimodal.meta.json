{"data_mtime": 1749061348, "dep_lines": [27, 22, 23, 24, 25, 26, 27, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 20, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.audio_utils", "transformers.image_processing_utils", "transformers.image_utils", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.utils", "re", "typing", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "abc", "collections", "enum", "logging", "numpy", "torch", "torch._C", "torch._tensor", "transformers.feature_extraction_utils", "transformers.image_processing_base", "transformers.utils.generic", "transformers.utils.hub"], "hash": "4fc6304075121f0e8750ca61d961e5ce86ebc805", "id": "transformers.models.phi4_multimodal.processing_phi4_multimodal", "ignore_all": true, "interface_hash": "4d7b1d1569a40f71cccb2f06c011c5445e5c3ab3", "mtime": 1749058951, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\phi4_multimodal\\processing_phi4_multimodal.py", "plugin_data": null, "size": 9138, "suppressed": [], "version_id": "1.16.0"}