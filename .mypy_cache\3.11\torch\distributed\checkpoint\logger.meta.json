{"data_mtime": 1749061365, "dep_lines": [10, 9, 9, 2, 3, 4, 5, 6, 7, 9, 1, 1, 1], "dep_prios": [5, 10, 20, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30], "dependencies": ["torch.distributed.checkpoint.logging_handlers", "torch.distributed.c10d_logger", "torch.distributed", "functools", "logging", "time", "typing", "typing_extensions", "uuid", "torch", "builtins", "_frozen_importlib", "abc"], "hash": "815c2be0912e12e4d21fddfd3946f524af4ae55f", "id": "torch.distributed.checkpoint.logger", "ignore_all": true, "interface_hash": "762cdb97a9bb44f70e1f9b82e32244748891371a", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\logger.py", "plugin_data": null, "size": 3667, "suppressed": [], "version_id": "1.16.0"}