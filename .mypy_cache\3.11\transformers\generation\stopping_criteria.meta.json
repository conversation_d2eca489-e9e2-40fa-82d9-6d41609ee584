{"data_mtime": 1749061347, "dep_lines": [10, 14, 10, 12, 13, 14, 1, 2, 3, 4, 5, 6, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.functional", "transformers.utils.logging", "torch.nn", "transformers.pytorch_utils", "transformers.tokenization_utils_base", "transformers.utils", "time", "warnings", "abc", "collections", "copy", "typing", "numpy", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.types", "transformers.utils.doc", "transformers.utils.hub", "types", "typing_extensions"], "hash": "11721655d1c6658ca49b5334f447f1b5524fbb65", "id": "transformers.generation.stopping_criteria", "ignore_all": true, "interface_hash": "8fe5c319cccb7854aabca9a11c0f1542e3a17e49", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\generation\\stopping_criteria.py", "plugin_data": null, "size": 28951, "suppressed": [], "version_id": "1.16.0"}