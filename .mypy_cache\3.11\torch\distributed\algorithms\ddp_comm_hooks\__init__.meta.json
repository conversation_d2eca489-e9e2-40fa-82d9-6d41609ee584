{"data_mtime": 1749061371, "dep_lines": [7, 7, 7, 7, 7, 5, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.algorithms.ddp_comm_hooks.debugging_hooks", "torch.distributed.algorithms.ddp_comm_hooks.default_hooks", "torch.distributed.algorithms.ddp_comm_hooks.optimizer_overlap_hooks", "torch.distributed.algorithms.ddp_comm_hooks.powerSGD_hook", "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks", "torch.distributed", "enum", "functools", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.futures", "typing"], "hash": "e339a5ca4f788b16f0d0875a42b82fcdea70fbd3", "id": "torch.distributed.algorithms.ddp_comm_hooks", "ignore_all": true, "interface_hash": "616a9ba3cbc4f1243df3e1dec239c4ff7431a116", "mtime": 1749057439, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\algorithms\\ddp_comm_hooks\\__init__.py", "plugin_data": null, "size": 3707, "suppressed": [], "version_id": "1.16.0"}