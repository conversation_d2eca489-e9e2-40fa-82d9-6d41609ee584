{".class": "MypyFile", "_fullname": "torch.distributed.tensor.parallel._utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DeviceMesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.DeviceMesh", "kind": "Gdef"}, "LayoutsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.tensor.parallel._utils.LayoutsType", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "Instance", "args": ["torch.distributed.tensor.placement_types.Placement"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}}}, "Placement": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Placement", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel._utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel._utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel._utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel._utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel._utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel._utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_deprecate_warnings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["func_name", "extra_msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel._utils._deprecate_warnings", "name": "_deprecate_warnings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["func_name", "extra_msg"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deprecate_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mesh_resources": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh._mesh_resources", "kind": "Gdef"}, "_validate_tp_mesh_dim": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel._utils._validate_tp_mesh_dim", "name": "_validate_tp_mesh_dim", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["device_mesh"], "arg_types": ["torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_tp_mesh_dim", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_torchdynamo_compiling": {".class": "SymbolTableNode", "cross_ref": "torch._dynamo.external_utils.is_compiling", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\_utils.py"}