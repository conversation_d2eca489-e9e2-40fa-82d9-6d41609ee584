{"data_mtime": 1749061371, "dep_lines": [1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["torch._inductor.package.package", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "5d6ec0811921ab5a1036895d1c3d8939c71e79ea", "id": "torch._inductor.package", "ignore_all": true, "interface_hash": "1478d2e59fcee1144ceb72562a09dc4e3940b1e8", "mtime": 1749057441, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\_inductor\\package\\__init__.py", "plugin_data": null, "size": 68, "suppressed": [], "version_id": "1.16.0"}