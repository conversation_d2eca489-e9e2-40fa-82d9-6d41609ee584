{"data_mtime": 1749061351, "dep_lines": [36, 22, 35, 17, 22, 23, 26, 27, 33, 34, 35, 17, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 20, 5, 5, 5, 5, 5, 5, 20, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.vit.configuration_vit", "torch.utils.checkpoint", "transformers.utils.logging", "collections.abc", "torch.utils", "torch.nn", "transformers.activations", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.pytorch_utils", "transformers.utils", "collections", "math", "typing", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.init", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.pixelshuffle", "torch.nn.parameter", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "8c02dd8a36e02c7d5c23733368881881787836cd", "id": "transformers.models.vit.modeling_vit", "ignore_all": true, "interface_hash": "61179a3db3b3c628f5b97789bb58355f143356e7", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vit\\modeling_vit.py", "plugin_data": null, "size": 34103, "suppressed": [], "version_id": "1.16.0"}