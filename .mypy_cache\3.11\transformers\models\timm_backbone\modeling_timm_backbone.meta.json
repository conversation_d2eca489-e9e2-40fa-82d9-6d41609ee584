{"data_mtime": 1749061352, "dep_lines": [24, 23, 89, 20, 21, 22, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28], "dep_prios": [5, 5, 20, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.timm_backbone.configuration_timm_backbone", "transformers.utils.backbone_utils", "transformers.models.timm_backbone", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "typing", "torch", "builtins", "_frozen_importlib", "abc", "collections", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "typing_extensions"], "hash": "1ed2c15e38f0d03c8baaba117067780572e02ba5", "id": "transformers.models.timm_backbone.modeling_timm_backbone", "ignore_all": true, "interface_hash": "693850de55bb69a7ed6defcbaa792e10b8cda190", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\timm_backbone\\modeling_timm_backbone.py", "plugin_data": null, "size": 6649, "suppressed": ["timm"], "version_id": "1.16.0"}