{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint._hf_storage", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.BytesStorageMetadata", "kind": "Gdef", "module_public": false}, "FILE_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._hf_storage.FILE_NAME", "name": "FILE_NAME", "setter_type": null, "type": "builtins.str"}}, "FsspecReader": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._fsspec_filesystem.FsspecReader", "kind": "Gdef", "module_public": false}, "FsspecWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._fsspec_filesystem.FsspecWriter", "kind": "Gdef", "module_public": false}, "Future": {".class": "SymbolTableNode", "cross_ref": "torch.futures.Future", "kind": "Gdef", "module_public": false}, "LoadPlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlan", "kind": "Gdef", "module_public": false}, "LoadPlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlanner", "kind": "Gdef", "module_public": false}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.Metadata", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ReadItem": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.ReadItem", "kind": "Gdef", "module_public": false}, "STORAGE_TYPES": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STORAGE_TYPES", "kind": "Gdef", "module_public": false}, "SUFFIX": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._hf_storage.SUFFIX", "name": "SUFFIX", "setter_type": null, "type": "builtins.str"}}, "SavePlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlan", "kind": "Gdef", "module_public": false}, "SavePlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlanner", "kind": "Gdef", "module_public": false}, "StorageMeta": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.StorageMeta", "kind": "Gdef", "module_public": false}, "WriteItem": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.WriteItem", "kind": "Gdef", "module_public": false}, "WriteResult": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.WriteResult", "kind": "Gdef", "module_public": false}, "_HuggingFaceStorageReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint._fsspec_filesystem.FsspecReader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader", "name": "_HuggingFaceStorageReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.checkpoint._hf_storage", "mro": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader", "torch.distributed.checkpoint._fsspec_filesystem.FsspecReader", "torch.distributed.checkpoint.filesystem.FileSystemReader", "torch.distributed.checkpoint.storage.StorageReader", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "token"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _HuggingFaceStorageReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader.read_data", "name": "read_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "planner"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader", "torch.distributed.checkpoint.planner.LoadPlan", "torch.distributed.checkpoint.planner.LoadPlanner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_data of _HuggingFaceStorageReader", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader.read_metadata", "name": "read_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_metadata of _HuggingFaceStorageReader", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "storage_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader.storage_data", "name": "storage_data", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HuggingFaceStorageWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint._fsspec_filesystem.FsspecWriter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "name": "_HuggingFaceStorageWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.checkpoint._hf_storage", "mro": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "torch.distributed.checkpoint._fsspec_filesystem.FsspecWriter", "torch.distributed.checkpoint.filesystem.FileSystemWriter", "torch.distributed.checkpoint.filesystem._FileSystemWriter", "torch.distributed.checkpoint.storage.StorageWriter", "abc.ABC", "torch.distributed.checkpoint.staging.BlockingAsyncStager", "torch.distributed.checkpoint.staging.AsyncStager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "path", "fqn_to_index_mapping", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "path", "fqn_to_index_mapping", "token"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _HuggingFaceStorageWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fqn_to_index_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter._fqn_to_index_mapping", "name": "_fqn_to_index_mapping", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_gen_file_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "index", "largest_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter._gen_file_name", "name": "_gen_file_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "index", "largest_index"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gen_file_name of _HuggingFaceStorageWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_split_by_storage_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "storage_plan", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter._split_by_storage_plan", "name": "_split_by_storage_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "storage_plan", "items"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.WriteItem"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_by_storage_plan of _HuggingFaceStorageWriter", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.WriteItem"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter.finish", "name": "finish", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata", "results"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "torch.distributed.checkpoint.metadata.Metadata", {".class": "Instance", "args": [{".class": "Instance", "args": ["torch.distributed.checkpoint.storage.WriteResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finish of _HuggingFaceStorageWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metadata_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter.metadata_path", "name": "metadata_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata_path of _HuggingFaceStorageWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter.metadata_path", "name": "metadata_path", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata_path of _HuggingFaceStorageWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prepare_global_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "plans"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter.prepare_global_plan", "name": "prepare_global_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "plans"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_global_plan of _HuggingFaceStorageWriter", "ret_type": {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_local_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter.prepare_local_plan", "name": "prepare_local_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "plan"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "torch.distributed.checkpoint.planner.SavePlan"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_local_plan of _HuggingFaceStorageWriter", "ret_type": "torch.distributed.checkpoint.planner.SavePlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "planner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter.write_data", "name": "write_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plan", "planner"], "arg_types": ["torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "torch.distributed.checkpoint.planner.SavePlan", "torch.distributed.checkpoint.planner.SavePlanner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_data of _HuggingFaceStorageWriter", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["torch.distributed.checkpoint.storage.WriteResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._hf_storage.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._hf_storage.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._hf_storage.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._hf_storage.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._hf_storage.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._hf_storage.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._hf_storage.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_metadata_fn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._hf_storage._metadata_fn", "name": "_metadata_fn", "setter_type": null, "type": "builtins.str"}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "fsspec": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint._hf_storage.fsspec", "name": "fsspec", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "torch.distributed.checkpoint._hf_storage.fsspec", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "queue": {".class": "SymbolTableNode", "cross_ref": "queue", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_hf_storage.py"}