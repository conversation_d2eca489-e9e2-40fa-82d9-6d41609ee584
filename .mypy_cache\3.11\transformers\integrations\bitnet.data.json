{".class": "MypyFile", "_fullname": "transformers.integrations.bitnet", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ActQuant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.autograd.function.Function"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.bitnet.ActQuant", "name": "ActQuant", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.ActQuant", "has_param_spec_type": false, "metaclass_type": "torch.autograd.function.FunctionMeta", "metadata": {}, "module_name": "transformers.integrations.bitnet", "mro": ["transformers.integrations.bitnet.ActQuant", "torch.autograd.function.Function", "torch.autograd.function._SingleLevelFunction", "torch._C._FunctionBase", "torch.autograd.function.FunctionCtx", "torch.autograd.function._HookMixin", "builtins.object"], "names": {".class": "SymbolTable", "backward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "grad_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.integrations.bitnet.ActQuant.backward", "name": "backward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.integrations.bitnet.ActQuant.backward", "name": "backward", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "grad_output"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backward of ActQuant", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "activation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.integrations.bitnet.ActQuant.forward", "name": "forward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.integrations.bitnet.ActQuant.forward", "name": "forward", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "activation"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of ActQuant", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.bitnet.ActQuant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.bitnet.ActQuant", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoBitLinear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.linear.Linear"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.bitnet.AutoBitLinear", "name": "AutoBitLinear", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.AutoBitLinear", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.bitnet", "mro": ["transformers.integrations.bitnet.AutoBitLinear", "torch.nn.modules.linear.Linear", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "in_features", "out_features", "bias", "device", "dtype", "online_quant", "use_rms_norm", "rms_norm_eps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.bitnet.AutoBitLinear.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "in_features", "out_features", "bias", "device", "dtype", "online_quant", "use_rms_norm", "rms_norm_eps"], "arg_types": ["transformers.integrations.bitnet.AutoBitLinear", "builtins.int", "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AutoBitLinear", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.AutoBitLinear.forward", "name": "forward", "type": null}}, "load_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "state_dict", "prefix", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.AutoBitLinear.load_hook", "name": "load_hook", "type": null}}, "online_quant": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.bitnet.AutoBitLinear.online_quant", "name": "online_quant", "setter_type": null, "type": "builtins.bool"}}, "rms_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.bitnet.AutoBitLinear.rms_norm", "name": "rms_norm", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.models.llama.modeling_llama.LlamaRMSNorm", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.bitnet.AutoBitLinear.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.bitnet.AutoBitLinear", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitLinear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.bitnet.BitLinear", "name": "BitLinear", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.BitLinear", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.bitnet", "mro": ["transformers.integrations.bitnet.BitLinear", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "in_features", "out_features", "bias", "device", "dtype", "use_rms_norm", "rms_norm_eps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.bitnet.BitLinear.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "in_features", "out_features", "bias", "device", "dtype", "use_rms_norm", "rms_norm_eps"], "arg_types": ["transformers.integrations.bitnet.BitLinear", "builtins.int", "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BitLinear", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation_quant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "num_bits"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.integrations.bitnet.BitLinear.activation_quant", "name": "activation_quant", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.integrations.bitnet.BitLinear.activation_quant", "name": "activation_quant", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "num_bits"], "arg_types": ["transformers.integrations.bitnet.BitLinear", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "activation_quant of BitLinear", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.bitnet.BitLinear.bias", "name": "bias", "setter_type": null, "type": {".class": "NoneType"}}}, "dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.bitnet.BitLinear.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.BitLinear.forward", "name": "forward", "type": null}}, "in_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.bitnet.BitLinear.in_features", "name": "in_features", "setter_type": null, "type": "builtins.int"}}, "out_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.bitnet.BitLinear.out_features", "name": "out_features", "setter_type": null, "type": "builtins.int"}}, "post_quant_process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input", "input_scale", "weight_scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.integrations.bitnet.BitLinear.post_quant_process", "name": "post_quant_process", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.integrations.bitnet.BitLinear.post_quant_process", "name": "post_quant_process", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input", "input_scale", "weight_scale"], "arg_types": ["transformers.integrations.bitnet.BitLinear", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post_quant_process of BitLinear", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rms_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.bitnet.BitLinear.rms_norm", "name": "rms_norm", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.models.llama.modeling_llama.LlamaRMSNorm", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.bitnet.BitLinear.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.bitnet.BitLinear", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "VALUES_PER_ITEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.bitnet.VALUES_PER_ITEM", "name": "VALUES_PER_ITEM", "setter_type": null, "type": "builtins.int"}}, "WeightQuant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.autograd.function.Function"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.bitnet.WeightQuant", "name": "WeightQuant", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.WeightQuant", "has_param_spec_type": false, "metaclass_type": "torch.autograd.function.FunctionMeta", "metadata": {}, "module_name": "transformers.integrations.bitnet", "mro": ["transformers.integrations.bitnet.WeightQuant", "torch.autograd.function.Function", "torch.autograd.function._SingleLevelFunction", "torch._C._FunctionBase", "torch.autograd.function.FunctionCtx", "torch.autograd.function._HookMixin", "builtins.object"], "names": {".class": "SymbolTable", "backward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "grad_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.integrations.bitnet.WeightQuant.backward", "name": "backward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.integrations.bitnet.WeightQuant.backward", "name": "backward", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "grad_output"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backward of WeightQuant", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.integrations.bitnet.WeightQuant.forward", "name": "forward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.integrations.bitnet.WeightQuant.forward", "name": "forward", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "weight"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of WeightQuant", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.bitnet.WeightQuant.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.bitnet.WeightQuant", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.bitnet.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.bitnet.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.bitnet.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.bitnet.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.bitnet.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.bitnet.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_replace_with_bitnet_linear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["model", "modules_to_not_convert", "current_key_name", "quantization_config", "has_been_replaced", "pre_quantized"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet._replace_with_bitnet_linear", "name": "_replace_with_bitnet_linear", "type": null}}, "init_empty_weights": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.integrations.bitnet.init_empty_weights", "name": "init_empty_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.integrations.bitnet.init_empty_weights", "source_any": null, "type_of_any": 3}}}, "is_accelerate_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_accelerate_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.bitnet.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "pack_weights": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["quantized_weights"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.pack_weights", "name": "pack_weights", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["quantized_weights"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pack_weights", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replace_with_bitnet_linear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["model", "modules_to_not_convert", "current_key_name", "quantization_config", "pre_quantized"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.bitnet.replace_with_bitnet_linear", "name": "replace_with_bitnet_linear", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "unpack_weights": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["packed", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.integrations.bitnet.unpack_weights", "name": "unpack_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["packed", "dtype"], "arg_types": ["torch._tensor.Tensor", "torch._C.dtype"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unpack_weights", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "transformers.integrations.bitnet.unpack_weights", "name": "unpack_weights", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["packed", "dtype"], "arg_types": ["torch._tensor.Tensor", "torch._C.dtype"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unpack_weights", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\integrations\\bitnet.py"}