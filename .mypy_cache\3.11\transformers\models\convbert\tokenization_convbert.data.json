{".class": "MypyFile", "_fullname": "transformers.models.convbert.tokenization_convbert", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BasicTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer", "name": "BasicTokenizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.convbert.tokenization_convbert", "mro": ["transformers.models.convbert.tokenization_convbert.BasicTokenizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "do_lower_case", "never_split", "tokenize_chinese_chars", "strip_accents", "do_split_on_punc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer.__init__", "name": "__init__", "type": null}}, "_clean_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer._clean_text", "name": "_clean_text", "type": null}}, "_is_chinese_char": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer._is_chinese_char", "name": "_is_chinese_char", "type": null}}, "_run_split_on_punc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "never_split"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer._run_split_on_punc", "name": "_run_split_on_punc", "type": null}}, "_run_strip_accents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer._run_strip_accents", "name": "_run_strip_accents", "type": null}}, "_tokenize_chinese_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer._tokenize_chinese_chars", "name": "_tokenize_chinese_chars", "type": null}}, "do_lower_case": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer.do_lower_case", "name": "do_lower_case", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "do_split_on_punc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer.do_split_on_punc", "name": "do_split_on_punc", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "never_split": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer.never_split", "name": "never_split", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "strip_accents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer.strip_accents", "name": "strip_accents", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "never_split"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer.tokenize", "name": "tokenize", "type": null}}, "tokenize_chinese_chars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer.tokenize_chinese_chars", "name": "tokenize_chinese_chars", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.convbert.tokenization_convbert.BasicTokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.convbert.tokenization_convbert.BasicTokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConvBertTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.tokenization_utils.PreTrainedTokenizer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer", "name": "ConvBertTokenizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.convbert.tokenization_convbert", "mro": ["transformers.models.convbert.tokenization_convbert.ConvBertTokenizer", "transformers.tokenization_utils.PreTrainedTokenizer", "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "transformers.tokenization_utils_base.SpecialTokensMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vocab_file", "do_lower_case", "do_basic_tokenize", "never_split", "unk_token", "sep_token", "pad_token", "cls_token", "mask_token", "tokenize_chinese_chars", "strip_accents", "clean_up_tokenization_spaces", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.__init__", "name": "__init__", "type": null}}, "_convert_id_to_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer._convert_id_to_token", "name": "_convert_id_to_token", "type": null}}, "_convert_token_to_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer._convert_token_to_id", "name": "_convert_token_to_id", "type": null}}, "_tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "split_special_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer._tokenize", "name": "_tokenize", "type": null}}, "basic_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.basic_tokenizer", "name": "basic_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "build_inputs_with_special_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "token_ids_0", "token_ids_1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.build_inputs_with_special_tokens", "name": "build_inputs_with_special_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "token_ids_0", "token_ids_1"], "arg_types": ["transformers.models.convbert.tokenization_convbert.ConvBertTokenizer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_inputs_with_special_tokens of ConvBertTokenizer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_tokens_to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.convert_tokens_to_string", "name": "convert_tokens_to_string", "type": null}}, "create_token_type_ids_from_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "token_ids_0", "token_ids_1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.create_token_type_ids_from_sequences", "name": "create_token_type_ids_from_sequences", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "token_ids_0", "token_ids_1"], "arg_types": ["transformers.models.convbert.tokenization_convbert.ConvBertTokenizer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_token_type_ids_from_sequences of ConvBertTokenizer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_basic_tokenize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.do_basic_tokenize", "name": "do_basic_tokenize", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "do_lower_case": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.do_lower_case", "name": "do_lower_case", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.do_lower_case", "name": "do_lower_case", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.convbert.tokenization_convbert.ConvBertTokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_lower_case of ConvBertTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_special_tokens_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "token_ids_0", "token_ids_1", "already_has_special_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.get_special_tokens_mask", "name": "get_special_tokens_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "token_ids_0", "token_ids_1", "already_has_special_tokens"], "arg_types": ["transformers.models.convbert.tokenization_convbert.ConvBertTokenizer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_special_tokens_mask of ConvBertTokenizer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_vocab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.get_vocab", "name": "get_vocab", "type": null}}, "ids_to_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.ids_to_tokens", "name": "ids_to_tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "save_vocabulary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "save_directory", "filename_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.save_vocabulary", "name": "save_vocabulary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "save_directory", "filename_prefix"], "arg_types": ["transformers.models.convbert.tokenization_convbert.ConvBertTokenizer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_vocabulary of ConvBertTokenizer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vocab": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.vocab", "name": "vocab", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab_files_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.vocab_files_names", "name": "vocab_files_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "vocab_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.vocab_size", "name": "vocab_size", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.vocab_size", "name": "vocab_size", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.convbert.tokenization_convbert.ConvBertTokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vocab_size of ConvBertTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "wordpiece_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.wordpiece_tokenizer", "name": "wordpiece_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.convbert.tokenization_convbert.ConvBertTokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils.PreTrainedTokenizer", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "VOCAB_FILES_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.convbert.tokenization_convbert.VOCAB_FILES_NAMES", "name": "VOCAB_FILES_NAMES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "WordpieceTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer", "name": "WordpieceTokenizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.convbert.tokenization_convbert", "mro": ["transformers.models.convbert.tokenization_convbert.WordpieceTokenizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "vocab", "unk_token", "max_input_chars_per_word"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer.__init__", "name": "__init__", "type": null}}, "max_input_chars_per_word": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer.max_input_chars_per_word", "name": "max_input_chars_per_word", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer.tokenize", "name": "tokenize", "type": null}}, "unk_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer.unk_token", "name": "unk_token", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer.vocab", "name": "vocab", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.convbert.tokenization_convbert.WordpieceTokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.convbert.tokenization_convbert.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.convbert.tokenization_convbert.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.convbert.tokenization_convbert.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.convbert.tokenization_convbert.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.convbert.tokenization_convbert.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.convbert.tokenization_convbert.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.convbert.tokenization_convbert.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_is_control": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils._is_control", "kind": "Gdef", "module_public": false}, "_is_punctuation": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils._is_punctuation", "kind": "Gdef", "module_public": false}, "_is_whitespace": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils._is_whitespace", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "load_vocab": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["vocab_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.load_vocab", "name": "load_vocab", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.convbert.tokenization_convbert.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "unicodedata": {".class": "SymbolTableNode", "cross_ref": "unicodedata", "kind": "Gdef", "module_public": false}, "whitespace_tokenize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.convbert.tokenization_convbert.whitespace_tokenize", "name": "whitespace_tokenize", "type": null}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\convbert\\tokenization_convbert.py"}