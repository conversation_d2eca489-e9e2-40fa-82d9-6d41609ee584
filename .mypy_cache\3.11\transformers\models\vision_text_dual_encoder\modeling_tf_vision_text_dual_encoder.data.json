{".class": "MypyFile", "_fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.configuration_auto.AutoConfig", "kind": "Gdef", "module_public": false}, "CLIPVisionConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.clip.configuration_clip.CLIPVisionConfig", "kind": "Gdef", "module_public": false}, "DUMMY_INPUTS": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.DUMMY_INPUTS", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef", "module_public": false}, "TFAutoModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModel", "kind": "Gdef", "module_public": false}, "TFCLIPOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.models.clip.modeling_tf_clip.TFCLIPOutput", "kind": "Gdef", "module_public": false}, "TFCLIPVisionModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.clip.modeling_tf_clip.TFCLIPVisionModel", "kind": "Gdef", "module_public": false}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef", "module_public": false}, "TFVisionTextDualEncoderModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_tf_utils.TFPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel", "name": "TFVisionTextDualEncoderModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder", "mro": ["transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "config", "vision_model", "text_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "config", "vision_model", "text_model"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel", {".class": "UnionType", "items": ["transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.modeling_tf_utils.TFPreTrainedModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.modeling_tf_utils.TFPreTrainedModel", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFVisionTextDualEncoderModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.built", "name": "built", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "pixel_values", "attention_mask", "position_ids", "return_loss", "token_type_ids", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "pixel_values", "attention_mask", "position_ids", "return_loss", "token_type_ids", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFVisionTextDualEncoderModel", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "transformers.models.clip.modeling_tf_clip.TFCLIPOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.call", "name": "call", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["projection_dim", "logit_scale_init_value", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dummy_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.dummy_inputs", "name": "dummy_inputs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.dummy_inputs", "name": "dummy_inputs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dummy_inputs of TFVisionTextDualEncoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_vision_text_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "vision_model_name_or_path", "text_model_name_or_path", "model_args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.from_vision_text_pretrained", "name": "from_vision_text_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "vision_model_name_or_path", "text_model_name_or_path", "model_args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_vision_text_pretrained of TFVisionTextDualEncoderModel", "ret_type": "transformers.modeling_tf_utils.TFPreTrainedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.from_vision_text_pretrained", "name": "from_vision_text_pretrained", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "vision_model_name_or_path", "text_model_name_or_path", "model_args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_vision_text_pretrained of TFVisionTextDualEncoderModel", "ret_type": "transformers.modeling_tf_utils.TFPreTrainedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_image_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.get_image_features", "name": "get_image_features", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.get_image_features", "name": "get_image_features", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "get_text_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "token_type_ids", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.get_text_features", "name": "get_text_features", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.get_text_features", "name": "get_text_features", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "load_weight_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.load_weight_prefix", "name": "load_weight_prefix", "setter_type": null, "type": "builtins.str"}}, "logit_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.logit_scale", "name": "logit_scale", "setter_type": null, "type": {".class": "NoneType"}}}, "projection_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.projection_dim", "name": "projection_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "text_embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.text_embed_dim", "name": "text_embed_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "text_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.text_model", "name": "text_model", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.modeling_tf_utils.TFPreTrainedModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "uses_pep604_syntax": false}}}, "text_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.text_projection", "name": "text_projection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "tf_to_pt_weight_rename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tf_weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.tf_to_pt_weight_rename", "name": "tf_to_pt_weight_rename", "type": null}}, "vision_embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.vision_embed_dim", "name": "vision_embed_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "vision_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.vision_model", "name": "vision_model", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.modeling_tf_utils.TFPreTrainedModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "uses_pep604_syntax": false}}}, "visual_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.visual_projection", "name": "visual_projection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.TFVisionTextDualEncoderModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "VISION_TEXT_DUAL_ENCODER_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.VISION_TEXT_DUAL_ENCODER_INPUTS_DOCSTRING", "name": "VISION_TEXT_DUAL_ENCODER_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "VISION_TEXT_DUAL_ENCODER_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.VISION_TEXT_DUAL_ENCODER_START_DOCSTRING", "name": "VISION_TEXT_DUAL_ENCODER_START_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "VISION_TEXT_DUAL_ENCODER_TEXT_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.VISION_TEXT_DUAL_ENCODER_TEXT_INPUTS_DOCSTRING", "name": "VISION_TEXT_DUAL_ENCODER_TEXT_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "VISION_TEXT_DUAL_ENCODER_VISION_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.VISION_TEXT_DUAL_ENCODER_VISION_INPUTS_DOCSTRING", "name": "VISION_TEXT_DUAL_ENCODER_VISION_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "VisionTextDualEncoderConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig", "kind": "Gdef", "module_public": false}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "clip_loss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["similarity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.clip_loss", "name": "clip_loss", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["similarity"], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clip_loss", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contrastive_loss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["logits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.contrastive_loss", "name": "contrastive_loss", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["logits"], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contrastive_loss", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "shape_list": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.shape_list", "kind": "Gdef", "module_public": false}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "name": "tf", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder.tf", "source_any": null, "type_of_any": 3}}}, "unpack_inputs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.unpack_inputs", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\modeling_tf_vision_text_dual_encoder.py"}