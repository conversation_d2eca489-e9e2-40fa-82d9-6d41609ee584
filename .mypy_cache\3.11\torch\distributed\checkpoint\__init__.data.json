{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.BytesStorageMetadata", "kind": "Gdef"}, "CheckpointException": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.api.CheckpointException", "kind": "Gdef"}, "ChunkStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.ChunkStorageMetadata", "kind": "Gdef"}, "DefaultLoadPlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "kind": "Gdef"}, "DefaultSavePlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "kind": "Gdef"}, "FileSystemReader": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.filesystem.FileSystemReader", "kind": "Gdef"}, "FileSystemWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.filesystem.FileSystemWriter", "kind": "Gdef"}, "LoadPlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlan", "kind": "Gdef"}, "LoadPlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlanner", "kind": "Gdef"}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.Metadata", "kind": "Gdef"}, "ReadItem": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.ReadItem", "kind": "Gdef"}, "SavePlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlan", "kind": "Gdef"}, "SavePlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlanner", "kind": "Gdef"}, "StorageReader": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.StorageReader", "kind": "Gdef"}, "StorageWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.StorageWriter", "kind": "Gdef"}, "TensorStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.TensorStorageMetadata", "kind": "Gdef"}, "WriteItem": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.WriteItem", "kind": "Gdef"}, "_HuggingFaceStorageReader": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageReader", "kind": "Gdef"}, "_HuggingFaceStorageWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._hf_storage._HuggingFaceStorageWriter", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_extension": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._extension", "kind": "Gdef"}, "async_save": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.state_dict_saver.async_save", "kind": "Gdef"}, "load": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.state_dict_loader.load", "kind": "Gdef"}, "load_sharded_optimizer_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.optimizer.load_sharded_optimizer_state_dict", "kind": "Gdef"}, "load_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.state_dict_loader.load_state_dict", "kind": "Gdef"}, "save": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.state_dict_saver.save", "kind": "Gdef"}, "save_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.state_dict_saver.save_state_dict", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\__init__.py"}