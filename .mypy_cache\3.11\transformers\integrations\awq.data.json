{".class": "MypyFile", "_fullname": "transformers.integrations.awq", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2FN", "kind": "Gdef"}, "AWQLinearVersion": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.AWQLinearVersion", "kind": "Gdef"}, "AWQ_FUSED_MAPPINGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.awq.AWQ_FUSED_MAPPINGS", "name": "AWQ_FUSED_MAPPINGS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AWQ_SCALES_MAPPINGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.awq.AWQ_SCALES_MAPPINGS", "name": "AWQ_SCALES_MAPPINGS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AwqBackendPackingMethod": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.AwqBackendPackingMethod", "kind": "Gdef"}, "AwqConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.AwqConfig", "kind": "Gdef"}, "ExllamaVersion": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.ExllamaVersion", "kind": "Gdef"}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.awq.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.awq.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.awq.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.awq.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.awq.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.awq.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_fuse_awq_attention_layers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["model", "module", "modules_to_fuse", "current_module_name", "target_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq._fuse_awq_attention_layers", "name": "_fuse_awq_attention_layers", "type": null}}, "_fuse_awq_layernorm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fuse_module_names", "module", "target_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq._fuse_awq_layernorm", "name": "_fuse_awq_layernorm", "type": null}}, "_fuse_awq_mlp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["model", "current_module_name", "fuse_module_names", "module", "target_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq._fuse_awq_mlp", "name": "_fuse_awq_mlp", "type": null}}, "fuse_awq_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "quantization_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq.fuse_awq_modules", "name": "fuse_awq_modules", "type": null}}, "get_modules_to_fuse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "quantization_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq.get_modules_to_fuse", "name": "get_modules_to_fuse", "type": null}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "is_auto_awq_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_auto_awq_available", "kind": "Gdef"}, "is_ipex_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_ipex_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.awq.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "post_init_awq_exllama_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "exllama_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq.post_init_awq_exllama_modules", "name": "post_init_awq_exllama_modules", "type": null}}, "post_init_awq_ipex_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq.post_init_awq_ipex_modules", "name": "post_init_awq_ipex_modules", "type": null}}, "replace_quantization_scales": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "model_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq.replace_quantization_scales", "name": "replace_quantization_scales", "type": null}}, "replace_with_awq_linear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["model", "modules_to_not_convert", "quantization_config", "current_key_name", "has_been_replaced"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.awq.replace_with_awq_linear", "name": "replace_with_awq_linear", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["model", "modules_to_not_convert", "quantization_config", "current_key_name", "has_been_replaced"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_with_awq_linear", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "packaging.version", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\integrations\\awq.py"}