{"data_mtime": 1749061363, "dep_lines": [34, 35, 36, 37, 27, 24, 25, 26, 27, 17, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.auto.modeling_tf_auto", "transformers.models.clip.modeling_tf_clip", "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder", "transformers.utils.logging", "transformers.configuration_utils", "transformers.modeling_tf_utils", "transformers.tf_utils", "transformers.utils", "__future__", "re", "typing", "builtins", "_frozen_importlib", "abc", "collections", "logging", "transformers.generation", "transformers.generation.tf_utils", "transformers.modeling_tf_outputs", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.models.clip", "transformers.models.clip.configuration_clip", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "0e8e8f32c6a548b48a14f586d9c154ed1019510b", "id": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder", "ignore_all": true, "interface_hash": "e1910b502262ab73a4cb82b26819a6bf157267f8", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\modeling_tf_vision_text_dual_encoder.py", "plugin_data": null, "size": 28707, "suppressed": ["tensorflow"], "version_id": "1.16.0"}