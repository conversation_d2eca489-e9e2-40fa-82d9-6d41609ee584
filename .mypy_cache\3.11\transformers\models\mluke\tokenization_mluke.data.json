{".class": "MypyFile", "_fullname": "transformers.models.mluke.tokenization_mluke", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddedToken": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.AddedToken", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BatchEncoding": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.BatchEncoding", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ENCODE_KWARGS_DOCSTRING": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.ENCODE_KWARGS_DOCSTRING", "kind": "Gdef", "module_public": false}, "ENCODE_PLUS_ADDITIONAL_KWARGS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mluke.tokenization_mluke.ENCODE_PLUS_ADDITIONAL_KWARGS_DOCSTRING", "name": "ENCODE_PLUS_ADDITIONAL_KWARGS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "EncodedInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.EncodedInput", "kind": "Gdef", "module_public": false}, "Entity": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "transformers.models.mluke.tokenization_mluke.Entity", "line": 48, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "EntityInput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "transformers.models.mluke.tokenization_mluke.EntityInput", "line": 49, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "EntitySpan": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "transformers.models.mluke.tokenization_mluke.EntitySpan", "line": 46, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "EntitySpanInput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "transformers.models.mluke.tokenization_mluke.EntitySpanInput", "line": 47, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpan"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "MLukeTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.tokenization_utils.PreTrainedTokenizer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.mluke.tokenization_mluke", "mro": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", "transformers.tokenization_utils.PreTrainedTokenizer", "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "transformers.tokenization_utils_base.SpecialTokensMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "text_pair", "entity_spans", "entity_spans_pair", "entities", "entities_pair", "add_special_tokens", "padding", "truncation", "max_length", "max_entity_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "text_pair", "entity_spans", "entity_spans_pair", "entities", "entities_pair", "add_special_tokens", "padding", "truncation", "max_length", "max_entity_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "kwargs"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.utils.generic.PaddingStrategy"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.tokenization_utils_base.TruncationStrategy"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of MLukeTokenizer", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.__call__", "name": "__call__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vocab_file", "entity_vocab_file", "bos_token", "eos_token", "sep_token", "cls_token", "unk_token", "pad_token", "mask_token", "task", "max_entity_length", "max_mention_length", "entity_token_1", "entity_token_2", "entity_unk_token", "entity_pad_token", "entity_mask_token", "entity_mask2_token", "sp_model_kwargs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vocab_file", "entity_vocab_file", "bos_token", "eos_token", "sep_token", "cls_token", "unk_token", "pad_token", "mask_token", "task", "max_entity_length", "max_mention_length", "entity_token_1", "entity_token_2", "entity_unk_token", "entity_pad_token", "entity_mask_token", "entity_mask2_token", "sp_model_kwargs", "kwargs"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MLukeTokenizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.__setstate__", "name": "__setstate__", "type": null}}, "_batch_encode_plus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "batch_text_or_text_pairs", "batch_entity_spans_or_entity_spans_pairs", "batch_entities_or_entities_pairs", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "max_entity_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._batch_encode_plus", "name": "_batch_encode_plus", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "batch_text_or_text_pairs", "batch_entity_spans_or_entity_spans_pairs", "batch_entities_or_entities_pairs", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "max_entity_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "kwargs"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.TextInputPair"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}, {".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}, {".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "transformers.utils.generic.PaddingStrategy", "transformers.tokenization_utils_base.TruncationStrategy", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_batch_encode_plus of MLukeTokenizer", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_batch_prepare_for_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "batch_ids_pairs", "batch_entity_ids_pairs", "batch_entity_token_spans_pairs", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "max_entity_length", "stride", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_length", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._batch_prepare_for_model", "name": "_batch_prepare_for_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "batch_ids_pairs", "batch_entity_ids_pairs", "batch_entity_token_spans_pairs", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "max_entity_length", "stride", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_length", "verbose"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "transformers.utils.generic.PaddingStrategy", "transformers.tokenization_utils_base.TruncationStrategy", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_batch_prepare_for_model of MLukeTokenizer", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._batch_prepare_for_model", "name": "_batch_prepare_for_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "_check_entity_input_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "entities", "entity_spans"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._check_entity_input_format", "name": "_check_entity_input_format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "entities", "entity_spans"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_entity_input_format of MLukeTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_id_to_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._convert_id_to_token", "name": "_convert_id_to_token", "type": null}}, "_convert_token_to_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._convert_token_to_id", "name": "_convert_token_to_id", "type": null}}, "_create_input_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "text_pair", "entities", "entities_pair", "entity_spans", "entity_spans_pair", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._create_input_sequence", "name": "_create_input_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "text_pair", "entities", "entities_pair", "entity_spans", "entity_spans_pair", "kwargs"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_input_sequence of MLukeTokenizer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_encode_plus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "text_pair", "entity_spans", "entity_spans_pair", "entities", "entities_pair", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "max_entity_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._encode_plus", "name": "_encode_plus", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "text_pair", "entity_spans", "entity_spans_pair", "entities", "entities_pair", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "max_entity_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "kwargs"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntitySpanInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.mluke.tokenization_mluke.EntityInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "transformers.utils.generic.PaddingStrategy", "transformers.tokenization_utils_base.TruncationStrategy", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_encode_plus of MLukeTokenizer", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "encoded_inputs", "max_length", "max_entity_length", "padding_strategy", "pad_to_multiple_of", "padding_side", "return_attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._pad", "name": "_pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "encoded_inputs", "max_length", "max_entity_length", "padding_strategy", "pad_to_multiple_of", "padding_side", "return_attention_mask"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.EncodedInput"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "transformers.tokenization_utils_base.BatchEncoding"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "transformers.utils.generic.PaddingStrategy", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pad of MLukeTokenizer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer._tokenize", "name": "_tokenize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tokenize of MLukeTokenizer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_inputs_with_special_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "token_ids_0", "token_ids_1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.build_inputs_with_special_tokens", "name": "build_inputs_with_special_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "token_ids_0", "token_ids_1"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_inputs_with_special_tokens of MLukeTokenizer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_tokens_to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.convert_tokens_to_string", "name": "convert_tokens_to_string", "type": null}}, "create_token_type_ids_from_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "token_ids_0", "token_ids_1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.create_token_type_ids_from_sequences", "name": "create_token_type_ids_from_sequences", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "token_ids_0", "token_ids_1"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_token_type_ids_from_sequences of MLukeTokenizer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "entity_mask2_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.entity_mask2_token_id", "name": "entity_mask2_token_id", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "entity_mask_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.entity_mask_token_id", "name": "entity_mask_token_id", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "entity_pad_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.entity_pad_token_id", "name": "entity_pad_token_id", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "entity_unk_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.entity_unk_token_id", "name": "entity_unk_token_id", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "entity_vocab": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.entity_vocab", "name": "entity_vocab", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "fairseq_ids_to_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.fairseq_ids_to_tokens", "name": "fairseq_ids_to_tokens", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fairseq_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.fairseq_offset", "name": "fairseq_offset", "setter_type": null, "type": "builtins.int"}}, "fairseq_tokens_to_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.fairseq_tokens_to_ids", "name": "fairseq_tokens_to_ids", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_special_tokens_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "token_ids_0", "token_ids_1", "already_has_special_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.get_special_tokens_mask", "name": "get_special_tokens_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "token_ids_0", "token_ids_1", "already_has_special_tokens"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_special_tokens_mask of MLukeTokenizer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_vocab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.get_vocab", "name": "get_vocab", "type": null}}, "max_entity_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.max_entity_length", "name": "max_entity_length", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "max_mention_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.max_mention_length", "name": "max_mention_length", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.model_input_names", "name": "model_input_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "encoded_inputs", "padding", "max_length", "max_entity_length", "pad_to_multiple_of", "padding_side", "return_attention_mask", "return_tensors", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.pad", "name": "pad", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "encoded_inputs", "padding", "max_length", "max_entity_length", "pad_to_multiple_of", "padding_side", "return_attention_mask", "return_tensors", "verbose"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "UnionType", "items": ["transformers.tokenization_utils_base.BatchEncoding", {".class": "Instance", "args": ["transformers.tokenization_utils_base.BatchEncoding"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.EncodedInput"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.EncodedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.EncodedInput"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.utils.generic.PaddingStrategy"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pad of M<PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_for_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "ids", "pair_ids", "entity_ids", "pair_entity_ids", "entity_token_spans", "pair_entity_token_spans", "add_special_tokens", "padding", "truncation", "max_length", "max_entity_length", "stride", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "prepend_batch_axis", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.prepare_for_model", "name": "prepare_for_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "ids", "pair_ids", "entity_ids", "pair_entity_ids", "entity_token_spans", "pair_entity_token_spans", "add_special_tokens", "padding", "truncation", "max_length", "max_entity_length", "stride", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "prepend_batch_axis", "kwargs"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.utils.generic.PaddingStrategy"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.tokenization_utils_base.TruncationStrategy"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_for_model of MLukeTokenizer", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.prepare_for_model", "name": "prepare_for_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "save_vocabulary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "save_directory", "filename_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.save_vocabulary", "name": "save_vocabulary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "save_directory", "filename_prefix"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_vocabulary of MLukeTokenizer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sp_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.sp_model", "name": "sp_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.mluke.tokenization_mluke.spm", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.mluke.tokenization_mluke.spm", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "sp_model_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.sp_model_kwargs", "name": "sp_model_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.task", "name": "task", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.vocab_file", "name": "vocab_file", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab_files_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.vocab_files_names", "name": "vocab_files_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "vocab_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.vocab_size", "name": "vocab_size", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.vocab_size", "name": "vocab_size", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.mluke.tokenization_mluke.MLukeTokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vocab_size of MLukeTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.mluke.tokenization_mluke.MLukeTokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PaddingStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.PaddingStrategy", "kind": "Gdef", "module_public": false}, "PreTrainedTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils.PreTrainedTokenizer", "kind": "Gdef", "module_public": false}, "SPIECE_UNDERLINE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mluke.tokenization_mluke.SPIECE_UNDERLINE", "name": "SPIECE_UNDERLINE", "setter_type": null, "type": "builtins.str"}}, "TensorType": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.TensorType", "kind": "Gdef", "module_public": false}, "TextInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.TextInput", "kind": "Gdef", "module_public": false}, "TextInputPair": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.TextInputPair", "kind": "Gdef", "module_public": false}, "TruncationStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.TruncationStrategy", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "VOCAB_FILES_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.mluke.tokenization_mluke.VOCAB_FILES_NAMES", "name": "VOCAB_FILES_NAMES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.mluke.tokenization_mluke.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mluke.tokenization_mluke.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mluke.tokenization_mluke.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mluke.tokenization_mluke.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mluke.tokenization_mluke.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mluke.tokenization_mluke.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mluke.tokenization_mluke.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_end_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_end_docstrings", "kind": "Gdef", "module_public": false}, "copyfile": {".class": "SymbolTableNode", "cross_ref": "shutil.copyfile", "kind": "Gdef", "module_public": false}, "is_tf_tensor": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.is_tf_tensor", "kind": "Gdef", "module_public": false}, "is_torch_tensor": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.is_torch_tensor", "kind": "Gdef", "module_public": false}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.mluke.tokenization_mluke.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "requires": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.requires", "kind": "Gdef", "module_public": false}, "spm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.mluke.tokenization_mluke.spm", "name": "spm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.mluke.tokenization_mluke.spm", "source_any": null, "type_of_any": 3}}}, "to_py_obj": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.to_py_obj", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\mluke\\tokenization_mluke.py"}