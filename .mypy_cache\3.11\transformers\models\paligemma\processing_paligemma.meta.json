{"data_mtime": 1749061348, "dep_lines": [36, 21, 22, 23, 31, 36, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.feature_extraction_utils", "transformers.image_utils", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.utils", "typing", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "abc", "collections", "enum", "logging", "numpy", "torch", "torch._C", "torch._tensor", "transformers.utils.generic", "transformers.utils.hub"], "hash": "ffe177c33863020b5d1df537c7cf27c918bc7169", "id": "transformers.models.paligemma.processing_paligemma", "ignore_all": true, "interface_hash": "7ba572f61a5ecef6327f6b33778d68b09bc7d5ba", "mtime": 1749058950, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\paligemma\\processing_paligemma.py", "plugin_data": null, "size": 15533, "suppressed": [], "version_id": "1.16.0"}