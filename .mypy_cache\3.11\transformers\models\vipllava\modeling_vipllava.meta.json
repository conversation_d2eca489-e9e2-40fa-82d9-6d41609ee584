{"data_mtime": 1749061351, "dep_lines": [34, 33, 26, 28, 29, 30, 31, 32, 22, 23, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.vipllava.configuration_vipllava", "transformers.models.auto", "torch.nn", "transformers.activations", "transformers.generation", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "dataclasses", "typing", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "transformers.cache_utils", "transformers.configuration_utils", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.models.auto.auto_factory", "transformers.models.auto.modeling_auto", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "0105d47baddd76b545bedc46e433261291f25877", "id": "transformers.models.vipllava.modeling_vipllava", "ignore_all": true, "interface_hash": "1a8c6e87b055d6be8e7aaf9aa2d102254a588490", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vipllava\\modeling_vipllava.py", "plugin_data": null, "size": 25186, "suppressed": [], "version_id": "1.16.0"}