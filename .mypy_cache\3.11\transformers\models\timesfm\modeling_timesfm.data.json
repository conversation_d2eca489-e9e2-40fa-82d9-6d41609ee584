{".class": "MypyFile", "_fullname": "transformers.models.timesfm.modeling_timesfm", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALL_ATTENTION_FUNCTIONS": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.ALL_ATTENTION_FUNCTIONS", "kind": "Gdef", "module_public": false}, "BaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutput", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef", "module_public": false}, "FlashAttentionKwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flash_attention_utils.FlashAttentionKwargs", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TimesFmAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention", "name": "TimesFmAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "layer_idx"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmAttention", "transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scale_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention._scale_query", "name": "_scale_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmAttention", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scale_query of TimesFmAttention", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.attention_dropout", "name": "attention_dropout", "setter_type": null, "type": "builtins.float"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.config", "name": "config", "setter_type": null, "type": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "hidden_states", "attention_mask", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "hidden_states", "attention_mask", "kwargs"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmAttention", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_flash_attention_utils.FlashAttentionKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TimesFmAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.head_dim", "name": "head_dim", "setter_type": null, "type": "builtins.int"}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.hidden_size", "name": "hidden_size", "setter_type": null, "type": "builtins.int"}}, "is_causal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.is_causal", "name": "is_causal", "setter_type": null, "type": "builtins.bool"}}, "k_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.k_proj", "name": "k_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "kv_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.kv_size", "name": "kv_size", "setter_type": null, "type": "builtins.int"}}, "layer_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.layer_idx", "name": "layer_idx", "setter_type": null, "type": "builtins.int"}}, "num_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.num_heads", "name": "num_heads", "setter_type": null, "type": "builtins.int"}}, "o_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.o_proj", "name": "o_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "q_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.q_proj", "name": "q_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "q_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.q_size", "name": "q_size", "setter_type": null, "type": "builtins.int"}}, "scaling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.scaling", "name": "scaling", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "v_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.v_proj", "name": "v_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "kind": "Gdef", "module_public": false}, "TimesFmDecoderLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer", "name": "TimesFmDecoderLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "layer_idx"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer", "transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmDecoderLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "hidden_states", "attention_mask", "paddings", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "hidden_states", "attention_mask", "paddings", "output_attentions"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TimesFmDecoder<PERSON><PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer.input_layernorm", "name": "input_layernorm", "setter_type": null, "type": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm"}}, "mlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer.mlp", "name": "mlp", "setter_type": null, "type": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP"}}, "self_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer.self_attn", "name": "self_attn", "setter_type": null, "type": "transformers.models.timesfm.modeling_timesfm.TimesFmAttention"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmDecoderLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmMLP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP", "name": "TimesFmMLP", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmMLP", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmMLP", "transformers.models.timesfm.configuration_timesfm.TimesFmConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmMLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "down_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP.down_proj", "name": "down_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "paddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP.forward", "name": "forward", "type": null}}, "gate_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP.gate_proj", "name": "gate_proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP.layer_norm", "name": "layer_norm", "setter_type": null, "type": "torch.nn.modules.normalization.LayerNorm"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmMLP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel", "name": "TimesFmModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmModel", "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmModel", "transformers.models.timesfm.configuration_timesfm.TimesFmConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_forward_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "patched_pads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel._forward_transform", "name": "_forward_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "patched_pads"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmModel", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_forward_transform of TimesFmModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_4d_attention_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["attention_mask", "sequence_length", "dtype", "device", "is_causal"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel._prepare_4d_attention_mask", "name": "_prepare_4d_attention_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["attention_mask", "sequence_length", "dtype", "device", "is_causal"], "arg_types": [{".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "torch._C.dtype", "torch._C.device", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_4d_attention_mask of TimesFmModel", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel._prepare_4d_attention_mask", "name": "_prepare_4d_attention_mask", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["attention_mask", "sequence_length", "dtype", "device", "is_causal"], "arg_types": [{".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "torch._C.dtype", "torch._C.device", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_4d_attention_mask of TimesFmModel", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_timesfm_masked_mean_std": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["inputs", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel._timesfm_masked_mean_std", "name": "_timesfm_masked_mean_std", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["inputs", "padding"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_timesfm_masked_mean_std of TimesFmModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel._timesfm_masked_mean_std", "name": "_timesfm_masked_mean_std", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["inputs", "padding"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_timesfm_masked_mean_std of TimesFmModel", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_timesfm_shift_padded_seq": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mask", "seq"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel._timesfm_shift_padded_seq", "name": "_timesfm_shift_padded_seq", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mask", "seq"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_timesfm_shift_padded_seq of TimesFmModel", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel._timesfm_shift_padded_seq", "name": "_timesfm_shift_padded_seq", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mask", "seq"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_timesfm_shift_padded_seq of TimesFmModel", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "past_values", "past_values_padding", "freq", "output_attentions", "output_hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "past_values", "past_values_padding", "freq", "output_attentions", "output_hidden_states"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmModel", "torch._tensor.Tensor", "torch._<PERSON><PERSON>", "torch._tensor.Tensor", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TimesFmModel", "ret_type": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freq_emb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel.freq_emb", "name": "freq_emb", "setter_type": null, "type": "torch.nn.modules.sparse.Embedding"}}, "input_ff_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel.input_ff_layer", "name": "input_ff_layer", "setter_type": null, "type": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock"}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel.layers", "name": "layers", "setter_type": null, "type": "torch.nn.modules.container.ModuleList"}}, "position_emb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel.position_emb", "name": "position_emb", "setter_type": null, "type": "transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmModelForPrediction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", "name": "TimesFmModelForPrediction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", "transformers.models.timesfm.configuration_timesfm.TimesFmConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmModelForPrediction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_postprocess_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_output", "stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction._postprocess_output", "name": "_postprocess_output", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_output", "stats"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", "torch._tensor.Tensor", {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_postprocess_output of TimesFmModelForPrediction", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "freq"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction._preprocess", "name": "_preprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "freq"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_preprocess of TimesFmModelForPrediction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_quantile_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "predictions", "targets"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction._quantile_loss", "name": "_quantile_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "predictions", "targets"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_quantile_loss of TimesFmModelForPrediction", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_timesfm_moving_average": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["arr", "window_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction._timesfm_moving_average", "name": "_timesfm_moving_average", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arr", "window_size"], "arg_types": ["torch._tensor.Tensor", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_timesfm_moving_average of TimesFmModelForPrediction", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction._timesfm_moving_average", "name": "_timesfm_moving_average", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["arr", "window_size"], "arg_types": ["torch._tensor.Tensor", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_timesfm_moving_average of TimesFmModelForPrediction", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "context_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction.context_len", "name": "context_len", "setter_type": null, "type": "builtins.int"}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction.decoder", "name": "decoder", "setter_type": null, "type": "transformers.models.timesfm.modeling_timesfm.TimesFmModel"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "past_values", "freq", "window_size", "future_values", "forecast_context_len", "return_forecast_on_context", "truncate_negative", "output_attentions", "output_hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "past_values", "freq", "window_size", "future_values", "forecast_context_len", "return_forecast_on_context", "truncate_negative", "output_attentions", "output_hidden_states"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["torch._tensor.Tensor", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of TimesFmModelForPrediction", "ret_type": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "horizon_ff_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction.horizon_ff_layer", "name": "horizon_ff_layer", "setter_type": null, "type": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock"}}, "horizon_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction.horizon_len", "name": "horizon_len", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmModelForPrediction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_outputs.BaseModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput", "name": "TimesFmOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "last_hidden_state", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "loc", "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "scale", "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmOutput", "transformers.modeling_outputs.BaseModelOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "hidden_states", "attentions", "loc", "scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "hidden_states", "attentions", "loc", "scale"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmOutput", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "last_hidden_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "loc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scale"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["last_hidden_state", "hidden_states", "attentions", "loc", "scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["last_hidden_state", "hidden_states", "attentions", "loc", "scale"], "arg_types": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TimesFmOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["last_hidden_state", "hidden_states", "attentions", "loc", "scale"], "arg_types": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TimesFmOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "loc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput.loc", "name": "loc", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "scale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput.scale", "name": "scale", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmOutputForPrediction": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_outputs.BaseModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction", "name": "TimesFmOutputForPrediction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "last_hidden_state", "type": {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 68, "name": "mean_predictions", "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 69, "name": "full_predictions", "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 70, "name": "loss", "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction", "transformers.modeling_outputs.BaseModelOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "hidden_states", "attentions", "mean_predictions", "full_predictions", "loss"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "hidden_states", "attentions", "mean_predictions", "full_predictions", "loss"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmOutputForPrediction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "last_hidden_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mean_predictions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "full_predictions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "loss"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["last_hidden_state", "hidden_states", "attentions", "mean_predictions", "full_predictions", "loss"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["last_hidden_state", "hidden_states", "attentions", "mean_predictions", "full_predictions", "loss"], "arg_types": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TimesFmOutputForPrediction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["last_hidden_state", "hidden_states", "attentions", "mean_predictions", "full_predictions", "loss"], "arg_types": [{".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TimesFmOutputForPrediction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "full_predictions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.full_predictions", "name": "full_predictions", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.loss", "name": "loss", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "mean_predictions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.mean_predictions", "name": "mean_predictions", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmOutputForPrediction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmPositionalEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding", "name": "TimesFmPositionalEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding", "transformers.models.timesfm.configuration_timesfm.TimesFmConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimesFmPositionalEmbedding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embedding_dims": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding.embedding_dims", "name": "embedding_dims", "setter_type": null, "type": "builtins.int"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "seq_length", "position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmPositionalEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel", "name": "TimesFmPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel._init_weights", "name": "_init_weights", "type": null}}, "_no_split_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel._no_split_modules", "name": "_no_split_modules", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_supports_sdpa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel._supports_sdpa", "name": "_supports_sdpa", "setter_type": null, "type": "builtins.bool"}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["patch_length", "context_length", "horizon_length", "freq_size", "num_hidden_layers", "hidden_size", "intermediate_size", "head_dim", "num_attention_heads", "tolerance", "rms_norm_eps", "quantiles", "pad_val", "attention_dropout", "use_positional_embedding", "initializer_range", "min_timescale", "max_timescale", "kwargs"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.float", "builtins.float", "builtins.bool", "builtins.float", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.timesfm.configuration_timesfm.TimesFmConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.timesfm.configuration_timesfm.TimesFmConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel.main_input_name", "name": "main_input_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmRMSNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm", "name": "TimesFmRMSNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_size", "eps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm.__init__", "name": "__init__", "type": null}}, "extra_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm.extra_repr", "name": "extra_repr", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm.forward", "name": "forward", "type": null}}, "variance_epsilon": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm.variance_epsilon", "name": "variance_epsilon", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm.weight", "name": "weight", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmRMSNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimesFmResidualBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock", "name": "TimesFmResidualBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.timesfm.modeling_timesfm", "mro": ["transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input_dims", "hidden_dims", "output_dims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.forward", "name": "forward", "type": null}}, "hidden_dims": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.hidden_dims", "name": "hidden_dims", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "input_dims": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.input_dims", "name": "input_dims", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "input_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.input_layer", "name": "input_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_dims": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.output_dims", "name": "output_dims", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.output_layer", "name": "output_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "residual_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.residual_layer", "name": "residual_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.timesfm.modeling_timesfm.TimesFmResidualBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.Unpack", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.timesfm.modeling_timesfm.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}, "can_return_tuple": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.can_return_tuple", "kind": "Gdef", "module_public": false}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.timesfm.modeling_timesfm.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "simple_eager_attention_forward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["module", "query_states", "key_states", "value_states", "attention_mask", "scaling", "dropout", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.timesfm.modeling_timesfm.simple_eager_attention_forward", "name": "simple_eager_attention_forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["module", "query_states", "key_states", "value_states", "attention_mask", "scaling", "dropout", "kwargs"], "arg_types": ["torch.nn.modules.module.Module", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "simple_eager_attention_forward", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "use_kernel_forward_from_hub": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.hub_kernels.use_kernel_forward_from_hub", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\timesfm\\modeling_timesfm.py"}