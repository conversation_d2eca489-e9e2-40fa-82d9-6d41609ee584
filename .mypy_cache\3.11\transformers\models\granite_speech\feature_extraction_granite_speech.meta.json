{"data_mtime": 1749061347, "dep_lines": [25, 26, 18, 23, 24, 25, 17, 19, 21, 32, 1, 1, 1, 1, 1, 1, 1, 1, 35], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.utils.logging", "transformers.utils.import_utils", "collections.abc", "transformers.feature_extraction_utils", "transformers.tokenization_utils_base", "transformers.utils", "math", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._tensor", "transformers.utils.hub"], "hash": "6be902a61db03a85f759933b30d86f3d6a9e5899", "id": "transformers.models.granite_speech.feature_extraction_granite_speech", "ignore_all": true, "interface_hash": "acd814350fac44dea91b427909ea84d1175f955b", "mtime": 1749058946, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\granite_speech\\feature_extraction_granite_speech.py", "plugin_data": null, "size": 8310, "suppressed": ["<PERSON><PERSON><PERSON>"], "version_id": "1.16.0"}