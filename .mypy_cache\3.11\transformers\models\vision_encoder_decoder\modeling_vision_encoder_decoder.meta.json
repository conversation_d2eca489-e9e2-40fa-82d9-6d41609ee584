{"data_mtime": 1749061351, "dep_lines": [30, 31, 32, 29, 23, 25, 26, 27, 28, 29, 17, 18, 19, 20, 22, 192, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.auto.modeling_auto", "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder", "transformers.utils.logging", "torch.nn", "transformers.configuration_utils", "transformers.generation", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "gc", "os", "tempfile", "typing", "torch", "transformers", "builtins", "_collections_abc", "_frozen_importlib", "abc", "collections", "logging", "ntpath", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "transformers.generation.tf_utils", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modeling_tf_utils", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.models.vision_encoder_decoder.modeling_tf_vision_encoder_decoder", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "72897c31f0cf3861a0a27afb9e4289d4dee7a02a", "id": "transformers.models.vision_encoder_decoder.modeling_vision_encoder_decoder", "ignore_all": true, "interface_hash": "5f7648e60cea928090aa8022fe0d8f99249da692", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vision_encoder_decoder\\modeling_vision_encoder_decoder.py", "plugin_data": null, "size": 29461, "suppressed": [], "version_id": "1.16.0"}