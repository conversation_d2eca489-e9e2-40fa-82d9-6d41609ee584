{"data_mtime": 1749061383, "dep_lines": [14, 17, 20, 23, 24, 25, 26, 27, 28, 29, 30, 33, 13, 31, 6, 12, 3, 4, 5, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.checkpoint._async_executor", "torch.distributed.checkpoint._async_process_executor", "torch.distributed.checkpoint._async_thread_executor", "torch.distributed.checkpoint._storage_utils", "torch.distributed.checkpoint.default_planner", "torch.distributed.checkpoint.logger", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.staging", "torch.distributed.checkpoint.stateful", "torch.distributed.checkpoint.storage", "torch.distributed.checkpoint.utils", "torch.distributed._state_dict_utils", "torch.distributed.distributed_c10d", "concurrent.futures", "torch.distributed", "inspect", "os", "warnings", "enum", "typing", "typing_extensions", "torch", "builtins", "_frozen_importlib", "abc", "concurrent", "concurrent.futures._base", "torch._C", "torch._C._distributed_c10d"], "hash": "3b7bbed6d79c4fc3e2af09bb91fddd15342d5e5d", "id": "torch.distributed.checkpoint.state_dict_saver", "ignore_all": true, "interface_hash": "58a65ca0d5190a60de03a7e4177b98f2be65d153", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\state_dict_saver.py", "plugin_data": null, "size": 14159, "suppressed": [], "version_id": "1.16.0"}