{".class": "MypyFile", "_fullname": "torch.distributed.elastic.multiprocessing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DefaultLogsSpecs": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.DefaultLogsSpecs", "kind": "Gdef"}, "LogsDest": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.LogsDest", "kind": "Gdef"}, "LogsSpecs": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.LogsSpecs", "kind": "Gdef"}, "MultiprocessContext": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.MultiprocessContext", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PContext": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.PContext", "kind": "Gdef"}, "ProcessFailure": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.errors.ProcessFailure", "kind": "Gdef"}, "RunProcsResult": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.RunProcsResult", "kind": "Gdef"}, "SignalException": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.SignalException", "kind": "Gdef"}, "Std": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.Std", "kind": "Gdef"}, "SubprocessContext": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.SubprocessContext", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.multiprocessing.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.multiprocessing.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_validate_full_rank": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api._validate_full_rank", "kind": "Gdef", "module_public": false}, "get_logger": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.utils.logging.get_logger", "kind": "Gdef", "module_public": false}, "start_processes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["name", "entrypoint", "args", "envs", "logs_specs", "log_line_prefixes", "start_method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.multiprocessing.start_processes", "name": "start_processes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["name", "entrypoint", "args", "envs", "logs_specs", "log_line_prefixes", "start_method"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.distributed.elastic.multiprocessing.api.LogsSpecs", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_processes", "ret_type": "torch.distributed.elastic.multiprocessing.api.PContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_map": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.to_map", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\elastic\\multiprocessing\\__init__.py"}