{"data_mtime": 1749061347, "dep_lines": [24, 23, 24, 17, 18, 19, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28, 408, 420, 431, 442, 527, 611], "dep_prios": [10, 5, 5, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 20, 20, 20, 20, 20], "dependencies": ["transformers.utils.logging", "transformers.tokenization_utils", "transformers.utils", "collections", "copy", "os", "unicodedata", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "genericpath", "logging", "ntpath", "transformers.tokenization_utils_base", "transformers.utils.hub", "transformers.utils.import_utils", "typing_extensions"], "hash": "4192287a6ef750766a1b806723604b038e0e19f7", "id": "transformers.models.bert_japanese.tokenization_bert_japanese", "ignore_all": true, "interface_hash": "410094e39d6fbe5d9ddc1cdf2d652ee39ad1c552", "mtime": 1749058940, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\bert_japanese\\tokenization_bert_japanese.py", "plugin_data": null, "size": 39088, "suppressed": ["sentencepiece", "fugashi", "ipadic", "unidic_lite", "unidic", "suda<PERSON><PERSON>", "rhoknp"], "version_id": "1.16.0"}