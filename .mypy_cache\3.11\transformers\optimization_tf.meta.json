{"data_mtime": 1749061363, "dep_lines": [28, 17, 18, 1, 1, 1, 26, 24, 20], "dep_prios": [5, 10, 5, 5, 30, 30, 5, 5, 10], "dependencies": ["transformers.modeling_tf_utils", "re", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "770a15e2552a707a84a4b86c484b60e131da5409", "id": "transformers.optimization_tf", "ignore_all": true, "interface_hash": "ba5ad42a67d838b98d03ec75bb9874e6f5634b25", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\optimization_tf.py", "plugin_data": null, "size": 16754, "suppressed": ["tensorflow.keras.optimizers.legacy", "tf_keras.optimizers.legacy", "tensorflow"], "version_id": "1.16.0"}