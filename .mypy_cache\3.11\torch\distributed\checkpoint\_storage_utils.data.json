{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint._storage_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FileSystemReader": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.filesystem.FileSystemReader", "kind": "Gdef"}, "FileSystemWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.filesystem.FileSystemWriter", "kind": "Gdef"}, "StorageReader": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.StorageReader", "kind": "Gdef"}, "StorageWriter": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.storage.StorageWriter", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._storage_utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._storage_utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._storage_utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._storage_utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._storage_utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._storage_utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_storage_setup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["storage", "checkpoint_id", "reader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._storage_utils._storage_setup", "name": "_storage_setup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["storage", "checkpoint_id", "reader"], "arg_types": [{".class": "UnionType", "items": ["torch.distributed.checkpoint.storage.StorageReader", "torch.distributed.checkpoint.storage.StorageWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_storage_setup", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "torch.distributed.checkpoint.storage.StorageReader", "torch.distributed.checkpoint.storage.StorageWriter"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\checkpoint\\_storage_utils.py"}