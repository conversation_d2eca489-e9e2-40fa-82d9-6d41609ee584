{".class": "MypyFile", "_fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2FN", "kind": "Gdef", "module_public": false}, "AMSoftmaxLoss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss", "name": "AMSoftmaxLoss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "input_dim", "num_labels", "scale", "margin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss.forward", "name": "forward", "type": null}}, "loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss.loss", "name": "loss", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "margin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss.margin", "name": "margin", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss.num_labels", "name": "num_labels", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss.scale", "name": "scale", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss.weight", "name": "weight", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.AMSoftmaxLoss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutput", "kind": "Gdef", "module_public": false}, "CausalLMOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.CausalLMOutput", "kind": "Gdef", "module_public": false}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.CrossEntropyLoss", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "SequenceClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.SequenceClassifierOutput", "kind": "Gdef", "module_public": false}, "TDNNLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer", "name": "TDN<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "layer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dilation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.dilation", "name": "dilation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.in_conv_dim", "name": "in_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kernel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.kernel", "name": "kernel", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kernel_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.kernel_size", "name": "kernel_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.out_conv_dim", "name": "out_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.TDNNLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TokenClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.TokenClassifierOutput", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Wav2Vec2BaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.Wav2Vec2BaseModelOutput", "kind": "Gdef", "module_public": false}, "Wav2Vec2BertAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter", "name": "Wav2Vec2BertAdapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.__init__", "name": "__init__", "type": null}}, "_compute_sub_sample_lengths_from_attention_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "seq_lens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter._compute_sub_sample_lengths_from_attention_mask", "name": "_compute_sub_sample_lengths_from_attention_mask", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.forward", "name": "forward", "type": null}}, "kernel_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.kernel_size", "name": "kernel_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layerdrop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.layerdrop", "name": "layerdrop", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.proj", "name": "proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "proj_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.proj_layer_norm", "name": "proj_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "stride": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.stride", "name": "stride", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertAdapterLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer", "name": "Wav2Vec2BertAdapterLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ffn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.ffn", "name": "ffn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ffn_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.ffn_layer_norm", "name": "ffn_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions", "sub_sampled_lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions", "sub_sampled_lengths"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertAdapterLayer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kernel_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.kernel_size", "name": "kernel_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "residual_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.residual_conv", "name": "residual_conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "residual_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.residual_layer_norm", "name": "residual_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "self_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.self_attn", "name": "self_attn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "self_attn_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.self_attn_conv", "name": "self_attn_conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "self_attn_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.self_attn_dropout", "name": "self_attn_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "self_attn_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.self_attn_layer_norm", "name": "self_attn_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "stride": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.stride", "name": "stride", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapterLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertBaseModelOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertBaseModelOutput", "line": 925, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "transformers.modeling_outputs.Wav2Vec2BaseModelOutput"}}, "Wav2Vec2BertConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.wav2vec2_bert.configuration_wav2vec2_bert.Wav2Vec2BertConfig", "kind": "Gdef", "module_public": false}, "Wav2Vec2BertConvolutionModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule", "name": "Wav2Vec2BertConvolutionModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "depthwise_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.depthwise_conv", "name": "depthwise_conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "depthwise_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.depthwise_layer_norm", "name": "depthwise_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.forward", "name": "forward", "type": null}}, "glu": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.glu", "name": "glu", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pointwise_conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.pointwise_conv1", "name": "pointwise_conv1", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pointwise_conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.pointwise_conv2", "name": "pointwise_conv2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertConvolutionModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder", "name": "Wav2Vec2BertEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder.__init__", "name": "__init__", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "embed_positions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder.embed_positions", "name": "embed_positions", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder.forward", "name": "forward", "type": null}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertEncoderLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer", "name": "Wav2Vec2BertEncoderLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.__init__", "name": "__init__", "type": null}}, "conv_module": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.conv_module", "name": "conv_module", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ffn1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.ffn1", "name": "ffn1", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ffn1_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.ffn1_layer_norm", "name": "ffn1_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ffn2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.ffn2", "name": "ffn2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ffn2_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.ffn2_layer_norm", "name": "ffn2_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "final_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.final_layer_norm", "name": "final_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "relative_position_embeddings", "output_attentions", "conv_attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "relative_position_embeddings", "output_attentions", "conv_attention_mask"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertEncoderLayer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "self_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.self_attn", "name": "self_attn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "self_attn_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.self_attn_dropout", "name": "self_attn_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "self_attn_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.self_attn_layer_norm", "name": "self_attn_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoderLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertFeatureProjection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection", "name": "Wav2Vec2BertFeatureProjection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection.__init__", "name": "__init__", "type": null}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection.forward", "name": "forward", "type": null}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection.projection", "name": "projection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertFeedForward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward", "name": "Wav2Vec2BertFeedForward", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "config", "act_fn", "hidden_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward.forward", "name": "forward", "type": null}}, "intermediate_act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward.intermediate_act_fn", "name": "intermediate_act_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward.intermediate_dense", "name": "intermediate_dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward.intermediate_dropout", "name": "intermediate_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward.output_dense", "name": "output_dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward.output_dropout", "name": "output_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertForAudioFrameClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification", "name": "Wav2Vec2BertForAudioFrameClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification", "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.__init__", "name": "__init__", "type": null}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertForAudioFrameClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.TokenClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freeze_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.freeze_base_model", "name": "freeze_base_model", "type": null}}, "layer_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.layer_weights", "name": "layer_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.num_labels", "name": "num_labels", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "wav2vec2_bert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.wav2vec2_bert", "name": "wav2vec2_bert", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForAudioFrameClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertForCTC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC", "name": "Wav2Vec2BertForCTC", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC", "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "target_lang"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "target_lang"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Wav2Vec2BertForCTC", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC.dropout", "name": "dropout", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertForCTC", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.CausalLMOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC.lm_head", "name": "lm_head", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "target_lang": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC.target_lang", "name": "target_lang", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "wav2vec2_bert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC.wav2vec2_bert", "name": "wav2vec2_bert", "setter_type": null, "type": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForCTC", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertForSequenceClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification", "name": "Wav2Vec2BertForSequenceClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification", "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.__init__", "name": "__init__", "type": null}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertForSequenceClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.SequenceClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freeze_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.freeze_base_model", "name": "freeze_base_model", "type": null}}, "layer_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.layer_weights", "name": "layer_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.projector", "name": "projector", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "wav2vec2_bert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.wav2vec2_bert", "name": "wav2vec2_bert", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForSequenceClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertForXVector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector", "name": "Wav2Vec2BertForXVector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector", "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.__init__", "name": "__init__", "type": null}}, "_get_tdnn_output_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector._get_tdnn_output_lengths", "name": "_get_tdnn_output_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input_lengths"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "builtins.int"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_tdnn_output_lengths of Wav2Vec2BertForXVector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feature_extractor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.feature_extractor", "name": "feature_extractor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "labels"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertForXVector", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.XVectorOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "freeze_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.freeze_base_model", "name": "freeze_base_model", "type": null}}, "layer_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.layer_weights", "name": "layer_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "objective": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.objective", "name": "objective", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.projector", "name": "projector", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tdnn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.tdnn", "name": "tdnn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "wav2vec2_bert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.wav2vec2_bert", "name": "wav2vec2_bert", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertForXVector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel", "name": "Wav2Vec2BertModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel", "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel", "transformers.models.wav2vec2_bert.configuration_wav2vec2_bert.Wav2Vec2BertConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Wav2Vec2BertModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mask_hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "mask_time_indices", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel._mask_hidden_states", "name": "_mask_hidden_states", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "mask_time_indices", "attention_mask"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel", "torch._<PERSON><PERSON>", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mask_hidden_states of Wav2Vec2BertModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adapter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.adapter", "name": "adapter", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertAdapter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.encoder", "name": "encoder", "setter_type": null, "type": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertEncoder"}}, "feature_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.feature_projection", "name": "feature_projection", "setter_type": null, "type": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeatureProjection"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "mask_time_indices", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "attention_mask", "mask_time_indices", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.Wav2Vec2BaseModelOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "intermediate_ffn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.intermediate_ffn", "name": "intermediate_ffn", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertFeedForward", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "masked_spec_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.masked_spec_embed", "name": "masked_spec_embed", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "name": "Wav2Vec2BertPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_get_feat_extract_output_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel._get_feat_extract_output_lengths", "name": "_get_feat_extract_output_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feat_extract_output_lengths of Wav2Vec2BertPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_feature_vector_attention_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "feature_vector_length", "attention_mask", "add_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel._get_feature_vector_attention_mask", "name": "_get_feature_vector_attention_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "feature_vector_length", "attention_mask", "add_adapter"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "builtins.int", "torch._<PERSON><PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feature_vector_attention_mask of Wav2Vec2BertPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel._init_weights", "name": "_init_weights", "type": null}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "hidden_size", "num_hidden_layers", "num_attention_heads", "intermediate_size", "feature_projection_input_dim", "hidden_act", "hidden_dropout", "activation_dropout", "attention_dropout", "feat_proj_dropout", "final_dropout", "layerdrop", "initializer_range", "layer_norm_eps", "apply_spec_augment", "mask_time_prob", "mask_time_length", "mask_time_min_masks", "mask_feature_prob", "mask_feature_length", "mask_feature_min_masks", "ctc_loss_reduction", "ctc_zero_infinity", "use_weighted_layer_sum", "classifier_proj_size", "tdnn_dim", "tdnn_kernel", "tdnn_dilation", "xvector_output_dim", "pad_token_id", "bos_token_id", "eos_token_id", "add_adapter", "adapter_kernel_size", "adapter_stride", "num_adapter_layers", "adapter_act", "use_intermediate_ffn_before_adapter", "output_hidden_size", "position_embeddings_type", "rotary_embedding_base", "max_source_positions", "left_max_position_embeddings", "right_max_position_embeddings", "conv_depthwise_kernel_size", "conformer_conv_dropout", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.wav2vec2_bert.configuration_wav2vec2_bert.Wav2Vec2BertConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.wav2vec2_bert.configuration_wav2vec2_bert.Wav2Vec2BertConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel.main_input_name", "name": "main_input_name", "setter_type": null, "type": "builtins.str"}}, "supports_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel.supports_gradient_checkpointing", "name": "supports_gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertRelPositionalEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding", "name": "Wav2Vec2BertRelPositionalEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding.__init__", "name": "__init__", "type": null}}, "d_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding.d_model", "name": "d_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "extend_pe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding.extend_pe", "name": "extend_pe", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertRelPositionalEmbedding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding.max_len", "name": "max_len", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding.pe", "name": "pe", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRelPositionalEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertRotaryPositionalEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding", "name": "Wav2Vec2BertRotaryPositionalEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding.__init__", "name": "__init__", "type": null}}, "cached_rotary_positional_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding.cached_rotary_positional_embedding", "name": "cached_rotary_positional_embedding", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cached_sequence_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding.cached_sequence_length", "name": "cached_sequence_length", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertRotaryPositionalEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Wav2Vec2BertSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention", "name": "Wav2Vec2BertSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert", "mro": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "is_adapter_attention"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.__init__", "name": "__init__", "type": null}}, "_apply_relative_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "key", "relative_position_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention._apply_relative_embeddings", "name": "_apply_relative_embeddings", "type": null}}, "_apply_rotary_embedding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "relative_position_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention._apply_rotary_embedding", "name": "_apply_rotary_embedding", "type": null}}, "distance_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.distance_embedding", "name": "distance_embedding", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "relative_position_embeddings", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "relative_position_embeddings", "output_attentions"], "arg_types": ["transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Wav2Vec2BertSelfAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.head_size", "name": "head_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "left_max_position_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.left_max_position_embeddings", "name": "left_max_position_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear_k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.linear_k", "name": "linear_k", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.linear_out", "name": "linear_out", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear_pos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.linear_pos", "name": "linear_pos", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear_q": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.linear_q", "name": "linear_q", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear_v": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.linear_v", "name": "linear_v", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.num_heads", "name": "num_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos_bias_u": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.pos_bias_u", "name": "pos_bias_u", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos_bias_v": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.pos_bias_v", "name": "pos_bias_v", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "position_embeddings_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.position_embeddings_type", "name": "position_embeddings_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "right_max_position_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.right_max_position_embeddings", "name": "right_max_position_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.Wav2Vec2BertSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "XVectorOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.XVectorOutput", "kind": "Gdef", "module_public": false}, "_HIDDEN_STATES_START_POSITION": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert._HIDDEN_STATES_START_POSITION", "name": "_HIDDEN_STATES_START_POSITION", "setter_type": null, "type": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_compute_mask_indices": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["shape", "mask_prob", "mask_length", "attention_mask", "min_masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert._compute_mask_indices", "name": "_compute_mask_indices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["shape", "mask_prob", "mask_length", "attention_mask", "min_masks"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.int", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_mask_indices", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_new_attention_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["hidden_states", "seq_lens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2_bert.modeling_wav2vec2_bert._compute_new_attention_mask", "name": "_compute_new_attention_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["hidden_states", "seq_lens"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_new_attention_mask", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_4d_attention_mask": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_attn_mask_utils._prepare_4d_attention_mask", "kind": "Gdef", "module_public": false}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}, "is_deepspeed_zero3_enabled": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.deepspeed.is_deepspeed_zero3_enabled", "kind": "Gdef", "module_public": false}, "is_fsdp_managed_module": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.fsdp.is_fsdp_managed_module", "kind": "Gdef", "module_public": false}, "is_peft_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_peft_available", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\wav2vec2_bert\\modeling_wav2vec2_bert.py"}