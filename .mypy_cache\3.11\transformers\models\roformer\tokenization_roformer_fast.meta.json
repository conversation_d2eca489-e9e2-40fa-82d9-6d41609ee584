{"data_mtime": 1749061348, "dep_lines": [25, 26, 24, 23, 24, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 21, 20], "dep_prios": [5, 5, 10, 5, 20, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["transformers.models.roformer.tokenization_roformer", "transformers.models.roformer.tokenization_utils", "transformers.utils.logging", "transformers.tokenization_utils_fast", "transformers.utils", "json", "typing", "builtins", "_frozen_importlib", "abc", "json.decoder", "logging", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils.hub"], "hash": "94cff4734eddbc0378c6ada66d47b59e90896302", "id": "transformers.models.roformer.tokenization_roformer_fast", "ignore_all": true, "interface_hash": "3a354c9b7e423b42c2f358678af172cb66789da9", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\roformer\\tokenization_roformer_fast.py", "plugin_data": null, "size": 6717, "suppressed": ["tokenizers.pre_tokenizers", "tokenizers"], "version_id": "1.16.0"}