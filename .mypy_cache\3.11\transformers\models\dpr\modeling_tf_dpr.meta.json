{"data_mtime": 1749061363, "dep_lines": [34, 35, 27, 25, 26, 27, 18, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.bert.modeling_tf_bert", "transformers.models.dpr.configuration_dpr", "transformers.utils.logging", "transformers.modeling_tf_outputs", "transformers.modeling_tf_utils", "transformers.utils", "__future__", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc", "collections", "logging", "numpy", "transformers.configuration_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.models.bert", "transformers.models.bert.configuration_bert", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub"], "hash": "4b25bd0c77059f5b8f6f2a168a1dc3087c475ec6", "id": "transformers.models.dpr.modeling_tf_dpr", "ignore_all": true, "interface_hash": "a95ea9908177112278955e6a490f2b5e2ce655ff", "mtime": 1749058945, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\dpr\\modeling_tf_dpr.py", "plugin_data": null, "size": 33959, "suppressed": ["tensorflow"], "version_id": "1.16.0"}