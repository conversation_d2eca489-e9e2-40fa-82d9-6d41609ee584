{".class": "MypyFile", "_fullname": "transformers.data.datasets", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "GlueDataTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.glue.GlueDataTrainingArguments", "kind": "Gdef"}, "GlueDataset": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.glue.GlueDataset", "kind": "Gdef"}, "LineByLineTextDataset": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.language_modeling.LineByLineTextDataset", "kind": "Gdef"}, "LineByLineWithRefDataset": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.language_modeling.LineByLineWithRefDataset", "kind": "Gdef"}, "LineByLineWithSOPTextDataset": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.language_modeling.LineByLineWithSOPTextDataset", "kind": "Gdef"}, "SquadDataTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.squad.SquadDataTrainingArguments", "kind": "Gdef"}, "SquadDataset": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.squad.SquadDataset", "kind": "Gdef"}, "TextDataset": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.language_modeling.TextDataset", "kind": "Gdef"}, "TextDatasetForNextSentencePrediction": {".class": "SymbolTableNode", "cross_ref": "transformers.data.datasets.language_modeling.TextDatasetForNextSentencePrediction", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.datasets.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.datasets.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.datasets.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.datasets.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.datasets.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.datasets.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.datasets.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\data\\datasets\\__init__.py"}