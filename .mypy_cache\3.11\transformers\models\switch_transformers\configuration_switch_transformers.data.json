{".class": "MypyFile", "_fullname": "transformers.models.switch_transformers.configuration_switch_transformers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef", "module_public": false}, "SwitchTransformersConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.configuration_utils.PretrainedConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", "name": "SwitchTransformersConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.switch_transformers.configuration_switch_transformers", "mro": ["transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vocab_size", "d_model", "d_kv", "d_ff", "expert_capacity", "num_layers", "num_sparse_encoder_layers", "num_decoder_layers", "num_sparse_decoder_layers", "num_heads", "num_experts", "router_bias", "router_jitter_noise", "router_dtype", "router_ignore_padding_tokens", "relative_attention_num_buckets", "relative_attention_max_distance", "dropout_rate", "layer_norm_epsilon", "router_z_loss_coef", "router_aux_loss_coef", "initializer_factor", "dense_act_fn", "is_encoder_decoder", "add_router_probs", "use_cache", "pad_token_id", "eos_token_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.__init__", "name": "__init__", "type": null}}, "add_router_probs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.add_router_probs", "name": "add_router_probs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "attribute_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.attribute_map", "name": "attribute_map", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "d_ff": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.d_ff", "name": "d_ff", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_kv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.d_kv", "name": "d_kv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.d_model", "name": "d_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "decoder_sparse_step": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.decoder_sparse_step", "name": "decoder_sparse_step", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dense_act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.dense_act_fn", "name": "dense_act_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout_rate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.dropout_rate", "name": "dropout_rate", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder_sparse_step": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.encoder_sparse_step", "name": "encoder_sparse_step", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "expert_capacity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.expert_capacity", "name": "expert_capacity", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "initializer_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.initializer_factor", "name": "initializer_factor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "keys_to_ignore_at_inference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.keys_to_ignore_at_inference", "name": "keys_to_ignore_at_inference", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "layer_norm_epsilon": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.layer_norm_epsilon", "name": "layer_norm_epsilon", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.model_type", "name": "model_type", "setter_type": null, "type": "builtins.str"}}, "num_decoder_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.num_decoder_layers", "name": "num_decoder_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_experts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.num_experts", "name": "num_experts", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.num_heads", "name": "num_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.num_layers", "name": "num_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_sparse_decoder_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.num_sparse_decoder_layers", "name": "num_sparse_decoder_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_sparse_encoder_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.num_sparse_encoder_layers", "name": "num_sparse_encoder_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "relative_attention_max_distance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.relative_attention_max_distance", "name": "relative_attention_max_distance", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "relative_attention_num_buckets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.relative_attention_num_buckets", "name": "relative_attention_num_buckets", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "router_aux_loss_coef": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.router_aux_loss_coef", "name": "router_aux_loss_coef", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "router_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.router_bias", "name": "router_bias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "router_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.router_dtype", "name": "router_dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "router_ignore_padding_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.router_ignore_padding_tokens", "name": "router_ignore_padding_tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "router_jitter_noise": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.router_jitter_noise", "name": "router_jitter_noise", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "router_z_loss_coef": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.router_z_loss_coef", "name": "router_z_loss_coef", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "use_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.use_cache", "name": "use_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.vocab_size", "name": "vocab_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.switch_transformers.configuration_switch_transformers.SwitchTransformersConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.switch_transformers.configuration_switch_transformers.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\switch_transformers\\configuration_switch_transformers.py"}