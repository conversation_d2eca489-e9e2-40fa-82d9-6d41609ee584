{"data_mtime": 1749061347, "dep_lines": [39, 23, 29, 36, 38, 23, 26, 28, 37, 38, 15, 16, 17, 18, 19, 21, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 10, 10, 5, 5, 5, 20, 10, 10, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.whisper.tokenization_whisper", "torch.nn.functional", "transformers.generation.logits_process", "transformers.generation.stopping_criteria", "transformers.utils.logging", "torch.nn", "transformers.cache_utils", "transformers.generation", "transformers.modeling_outputs", "transformers.utils", "copy", "math", "warnings", "zlib", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "_typeshed", "_warnings", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._C._nn", "torch._tensor", "transformers.generation.configuration_utils", "transformers.generation.utils", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "27b2c0fc182dbcc63d009c44b2d5321dca1b8c37", "id": "transformers.models.whisper.generation_whisper", "ignore_all": true, "interface_hash": "671f0abfce6e55d6bf27ce23edb057061889a850", "mtime": 1749058954, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\whisper\\generation_whisper.py", "plugin_data": null, "size": 102535, "suppressed": [], "version_id": "1.16.0"}