{"data_mtime": 1749061363, "dep_lines": [10, 13, 14, 1, 2, 3, 4, 5, 7, 9, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["packaging.version", "transformers.modelcard", "transformers.modeling_tf_utils", "logging", "os", "pathlib", "time", "typing", "numpy", "huggingface_hub", "transformers", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "huggingface_hub._space_api", "huggingface_hub.hf_api", "huggingface_hub.repository", "ntpath", "packaging", "transformers.tokenization_utils_base", "transformers.trainer_utils", "transformers.utils", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "2d2cd8eccadf5b46c334f959cfc1f70aa3ca7b80", "id": "transformers.keras_callbacks", "ignore_all": true, "interface_hash": "dcc790ba37579b5f0a32b5b1cf5130e662ca69c3", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\keras_callbacks.py", "plugin_data": null, "size": 20669, "suppressed": ["tensorflow"], "version_id": "1.16.0"}