{"data_mtime": 1749061348, "dep_lines": [27, 28, 26, 24, 25, 26, 17, 18, 19, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 32, 35], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["transformers.models.rag.configuration_rag", "transformers.models.rag.tokenization_rag", "transformers.utils.logging", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils", "os", "pickle", "time", "typing", "numpy", "builtins", "_frozen_importlib", "_io", "_pickle", "_typeshed", "abc", "collections", "io", "logging", "ntpath", "transformers.configuration_utils", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "1a1cdc8785edc9c77b32f0fa872dbae945938303", "id": "transformers.models.rag.retrieval_rag", "ignore_all": true, "interface_hash": "2a51b00ae8b16dc27a1ed1cd3473d434bc4b0ef4", "mtime": 1749058952, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\rag\\retrieval_rag.py", "plugin_data": null, "size": 29951, "suppressed": ["datasets", "faiss"], "version_id": "1.16.0"}