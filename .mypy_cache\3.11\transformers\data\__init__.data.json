{".class": "MypyFile", "_fullname": "transformers.data", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DataCollatorForLanguageModeling": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorForLanguageModeling", "kind": "Gdef"}, "DataCollatorForMultipleChoice": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorForMultipleChoice", "kind": "Gdef"}, "DataCollatorForPermutationLanguageModeling": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorForPermutationLanguageModeling", "kind": "Gdef"}, "DataCollatorForSOP": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorForSOP", "kind": "Gdef"}, "DataCollatorForSeq2Seq": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorForSeq2Seq", "kind": "Gdef"}, "DataCollatorForTokenClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorForTokenClassification", "kind": "Gdef"}, "DataCollatorForWholeWordMask": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorForWholeWordMask", "kind": "Gdef"}, "DataCollatorWithFlattening": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorWithFlattening", "kind": "Gdef"}, "DataCollatorWithPadding": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorWithPadding", "kind": "Gdef"}, "DataProcessor": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.utils.DataProcessor", "kind": "Gdef"}, "DefaultDataCollator": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DefaultDataCollator", "kind": "Gdef"}, "InputExample": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.utils.InputExample", "kind": "Gdef"}, "InputFeatures": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.utils.InputFeatures", "kind": "Gdef"}, "SingleSentenceClassificationProcessor": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.utils.SingleSentenceClassificationProcessor", "kind": "Gdef"}, "SquadExample": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.squad.SquadExample", "kind": "Gdef"}, "SquadFeatures": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.squad.SquadFeatures", "kind": "Gdef"}, "SquadV1Processor": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.squad.SquadV1Processor", "kind": "Gdef"}, "SquadV2Processor": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.squad.SquadV2Processor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.data.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "default_data_collator": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.default_data_collator", "kind": "Gdef"}, "glue_compute_metrics": {".class": "SymbolTableNode", "cross_ref": "transformers.data.metrics.glue_compute_metrics", "kind": "Gdef"}, "glue_convert_examples_to_features": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.glue.glue_convert_examples_to_features", "kind": "Gdef"}, "glue_output_modes": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.glue.glue_output_modes", "kind": "Gdef"}, "glue_processors": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.glue.glue_processors", "kind": "Gdef"}, "glue_tasks_num_labels": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.glue.glue_tasks_num_labels", "kind": "Gdef"}, "squad_convert_examples_to_features": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.squad.squad_convert_examples_to_features", "kind": "Gdef"}, "xnli_compute_metrics": {".class": "SymbolTableNode", "cross_ref": "transformers.data.metrics.xnli_compute_metrics", "kind": "Gdef"}, "xnli_output_modes": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.xnli.xnli_output_modes", "kind": "Gdef"}, "xnli_processors": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.xnli.xnli_processors", "kind": "Gdef"}, "xnli_tasks_num_labels": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.xnli.xnli_tasks_num_labels", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\data\\__init__.py"}