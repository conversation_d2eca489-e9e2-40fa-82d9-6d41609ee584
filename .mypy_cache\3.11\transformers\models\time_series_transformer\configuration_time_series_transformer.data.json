{".class": "MypyFile", "_fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef", "module_public": false}, "TimeSeriesTransformerConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.configuration_utils.PretrainedConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig", "name": "TimeSeriesTransformerConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.time_series_transformer.configuration_time_series_transformer", "mro": ["transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "prediction_length", "context_length", "distribution_output", "loss", "input_size", "lags_sequence", "scaling", "num_dynamic_real_features", "num_static_categorical_features", "num_static_real_features", "num_time_features", "cardinality", "embedding_dimension", "encoder_ffn_dim", "decoder_ffn_dim", "encoder_attention_heads", "decoder_attention_heads", "encoder_layers", "decoder_layers", "is_encoder_decoder", "activation_function", "d_model", "dropout", "encoder_layerdrop", "decoder_layerdrop", "attention_dropout", "activation_dropout", "num_parallel_samples", "init_std", "use_cache", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "prediction_length", "context_length", "distribution_output", "loss", "input_size", "lags_sequence", "scaling", "num_dynamic_real_features", "num_static_categorical_features", "num_static_real_features", "num_time_features", "cardinality", "embedding_dimension", "encoder_ffn_dim", "decoder_ffn_dim", "encoder_attention_heads", "decoder_attention_heads", "encoder_layers", "decoder_layers", "is_encoder_decoder", "activation_function", "d_model", "dropout", "encoder_layerdrop", "decoder_layerdrop", "attention_dropout", "activation_dropout", "num_parallel_samples", "init_std", "use_cache", "kwargs"], "arg_types": ["transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimeSeriesTransformerConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_number_of_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig._number_of_features", "name": "_number_of_features", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_number_of_features of TimeSeriesTransformerConfig", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig._number_of_features", "name": "_number_of_features", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_number_of_features of TimeSeriesTransformerConfig", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "activation_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.activation_dropout", "name": "activation_dropout", "setter_type": null, "type": "builtins.float"}}, "activation_function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.activation_function", "name": "activation_function", "setter_type": null, "type": "builtins.str"}}, "attention_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.attention_dropout", "name": "attention_dropout", "setter_type": null, "type": "builtins.float"}}, "attribute_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.attribute_map", "name": "attribute_map", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cardinality": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.cardinality", "name": "cardinality", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "context_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.context_length", "name": "context_length", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "d_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.d_model", "name": "d_model", "setter_type": null, "type": "builtins.int"}}, "decoder_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.decoder_attention_heads", "name": "decoder_attention_heads", "setter_type": null, "type": "builtins.int"}}, "decoder_ffn_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.decoder_ffn_dim", "name": "decoder_ffn_dim", "setter_type": null, "type": "builtins.int"}}, "decoder_layerdrop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.decoder_layerdrop", "name": "decoder_layerdrop", "setter_type": null, "type": "builtins.float"}}, "decoder_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.decoder_layers", "name": "decoder_layers", "setter_type": null, "type": "builtins.int"}}, "distribution_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.distribution_output", "name": "distribution_output", "setter_type": null, "type": "builtins.str"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.dropout", "name": "dropout", "setter_type": null, "type": "builtins.float"}}, "embedding_dimension": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.embedding_dimension", "name": "embedding_dimension", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "encoder_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.encoder_attention_heads", "name": "encoder_attention_heads", "setter_type": null, "type": "builtins.int"}}, "encoder_ffn_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.encoder_ffn_dim", "name": "encoder_ffn_dim", "setter_type": null, "type": "builtins.int"}}, "encoder_layerdrop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.encoder_layerdrop", "name": "encoder_layerdrop", "setter_type": null, "type": "builtins.float"}}, "encoder_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.encoder_layers", "name": "encoder_layers", "setter_type": null, "type": "builtins.int"}}, "feature_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.feature_size", "name": "feature_size", "setter_type": null, "type": "builtins.int"}}, "init_std": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.init_std", "name": "init_std", "setter_type": null, "type": "builtins.float"}}, "input_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.input_size", "name": "input_size", "setter_type": null, "type": "builtins.int"}}, "lags_sequence": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.lags_sequence", "name": "lags_sequence", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.loss", "name": "loss", "setter_type": null, "type": "builtins.str"}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.model_type", "name": "model_type", "setter_type": null, "type": "builtins.str"}}, "num_dynamic_real_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.num_dynamic_real_features", "name": "num_dynamic_real_features", "setter_type": null, "type": "builtins.int"}}, "num_parallel_samples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.num_parallel_samples", "name": "num_parallel_samples", "setter_type": null, "type": "builtins.int"}}, "num_static_categorical_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.num_static_categorical_features", "name": "num_static_categorical_features", "setter_type": null, "type": "builtins.int"}}, "num_static_real_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.num_static_real_features", "name": "num_static_real_features", "setter_type": null, "type": "builtins.int"}}, "num_time_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.num_time_features", "name": "num_time_features", "setter_type": null, "type": "builtins.int"}}, "prediction_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.prediction_length", "name": "prediction_length", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "scaling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.scaling", "name": "scaling", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "use_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.use_cache", "name": "use_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.time_series_transformer.configuration_time_series_transformer.TimeSeriesTransformerConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.time_series_transformer.configuration_time_series_transformer.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\time_series_transformer\\configuration_time_series_transformer.py"}