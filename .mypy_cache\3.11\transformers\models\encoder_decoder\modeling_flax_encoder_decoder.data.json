{".class": "MypyFile", "_fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.configuration_auto.AutoConfig", "kind": "Gdef", "module_public": false}, "ENCODER_DECODER_DECODE_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.ENCODER_DECODER_DECODE_INPUTS_DOCSTRING", "name": "ENCODER_DECODER_DECODE_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "ENCODER_DECODER_ENCODE_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.ENCODER_DECODER_ENCODE_INPUTS_DOCSTRING", "name": "ENCODER_DECODER_ENCODE_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "ENCODER_DECODER_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.ENCODER_DECODER_INPUTS_DOCSTRING", "name": "ENCODER_DECODER_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "ENCODER_DECODER_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.ENCODER_DECODER_START_DOCSTRING", "name": "ENCODER_DECODER_START_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "EncoderDecoderConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.encoder_decoder.configuration_encoder_decoder.EncoderDecoderConfig", "kind": "Gdef", "module_public": false}, "FlaxAutoModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_flax_auto.FlaxAutoModel", "kind": "Gdef", "module_public": false}, "FlaxAutoModelForCausalLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_flax_auto.FlaxAutoModelForCausalLM", "kind": "Gdef", "module_public": false}, "FlaxBaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxBaseModelOutput", "kind": "Gdef", "module_public": false}, "FlaxCausalLMOutputWithCrossAttentions": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxCausalLMOutputWithCrossAttentions", "kind": "Gdef", "module_public": false}, "FlaxEncoderDecoderModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_flax_utils.FlaxPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", "name": "FlaxEncoderDecoderModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder", "mro": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "position_ids", "decoder_position_ids", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "position_ids", "decoder_position_ids", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.PRNGKey", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxEncoderDecoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.__call__", "name": "__call__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "kwargs"], "arg_types": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", "transformers.models.encoder_decoder.configuration_encoder_decoder.EncoderDecoderConfig", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FlaxEncoderDecoderModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.encoder_decoder.configuration_encoder_decoder.EncoderDecoderConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.encoder_decoder.configuration_encoder_decoder.EncoderDecoderConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "decoder_position_ids", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "decoder_position_ids", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.PRNGKey", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of FlaxEncoderDecoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.decode", "name": "decode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.PRNGKey", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of FlaxEncoderDecoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.encode", "name": "encode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "from_encoder_decoder_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "encoder_pretrained_model_name_or_path", "decoder_pretrained_model_name_or_path", "model_args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.from_encoder_decoder_pretrained", "name": "from_encoder_decoder_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "encoder_pretrained_model_name_or_path", "decoder_pretrained_model_name_or_path", "model_args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_encoder_decoder_pretrained of FlaxEncoderDecoderModel", "ret_type": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.from_encoder_decoder_pretrained", "name": "from_encoder_decoder_pretrained", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "encoder_pretrained_model_name_or_path", "decoder_pretrained_model_name_or_path", "model_args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_encoder_decoder_pretrained of FlaxEncoderDecoderModel", "ret_type": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "init_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "batch_size", "max_length", "encoder_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.init_cache", "name": "init_cache", "type": null}}, "init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.init_weights", "name": "init_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "arg_types": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jax", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FrozenDict", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_weights of FlaxEncoderDecoderModel", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FrozenDict", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_inputs_for_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "decoder_input_ids", "max_length", "attention_mask", "decoder_attention_mask", "encoder_outputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.prepare_inputs_for_generation", "name": "prepare_inputs_for_generation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "decoder_input_ids", "max_length", "attention_mask", "decoder_attention_mask", "encoder_outputs", "kwargs"], "arg_types": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jax", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jax", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_inputs_for_generation of FlaxEncoderDecoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_inputs_for_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_outputs", "model_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.update_inputs_for_generation", "name": "update_inputs_for_generation", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxEncoderDecoderModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule", "name": "FlaxEncoderDecoderModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder", "mro": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "position_ids", "decoder_position_ids", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "decoder_input_ids", "decoder_attention_mask", "position_ids", "decoder_position_ids", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "arg_types": ["transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxEncoderDecoderModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_decoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule._get_decoder_module", "name": "_get_decoder_module", "type": null}}, "_get_encoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule._get_encoder_module", "name": "_get_encoder_module", "type": null}}, "_get_projection_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule._get_projection_module", "name": "_get_projection_module", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule.config", "name": "config", "setter_type": null, "type": "transformers.models.encoder_decoder.configuration_encoder_decoder.EncoderDecoderConfig"}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule.decoder", "name": "decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}}}, "enc_to_dec_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule.enc_to_dec_proj", "name": "enc_to_dec_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FlaxEncoderDecoderModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "kind": "Gdef", "module_public": false}, "FlaxSeq2SeqLMOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxSeq2SeqLMOutput", "kind": "Gdef", "module_public": false}, "FrozenDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FrozenDict", "name": "FrozenDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.FrozenDict", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PRNGKey": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.PRNGKey", "name": "PRNGKey", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.PRNGKey", "source_any": null, "type_of_any": 3}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "flatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.flatten_dict", "name": "flatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.flatten_dict", "source_any": null, "type_of_any": 3}}}, "freeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.freeze", "name": "freeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.freeze", "source_any": null, "type_of_any": 3}}}, "jax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jax", "name": "jax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jax", "source_any": null, "type_of_any": 3}}}, "jnp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "name": "jnp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.jnp", "source_any": null, "type_of_any": 3}}}, "lax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.lax", "name": "lax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.lax", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.nn", "name": "nn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.nn", "source_any": null, "type_of_any": 3}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "unflatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.unflatten_dict", "name": "unflatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.unflatten_dict", "source_any": null, "type_of_any": 3}}}, "unfreeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.unfreeze", "name": "unfreeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.encoder_decoder.modeling_flax_encoder_decoder.unfreeze", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\modeling_flax_encoder_decoder.py"}