{"data_mtime": 1749061348, "dep_lines": [30, 31, 29, 27, 28, 29, 17, 18, 19, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25], "dep_prios": [5, 5, 10, 5, 5, 20, 10, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.models.whisper.english_normalizer", "transformers.models.whisper.tokenization_whisper", "transformers.utils.logging", "transformers.tokenization_utils_base", "transformers.tokenization_utils_fast", "transformers.utils", "json", "os", "re", "warnings", "functools", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "collections", "enum", "io", "json.decoder", "logging", "transformers.tokenization_utils", "transformers.utils.hub", "types", "typing_extensions"], "hash": "37c88b06c13344ffdfb71243a1a76ce548949da4", "id": "transformers.models.whisper.tokenization_whisper_fast", "ignore_all": true, "interface_hash": "19e93ca3a4f1cc0cf4e985af80d60ab1d4d585b9", "mtime": 1749058954, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\whisper\\tokenization_whisper_fast.py", "plugin_data": null, "size": 30297, "suppressed": ["tokenizers"], "version_id": "1.16.0"}