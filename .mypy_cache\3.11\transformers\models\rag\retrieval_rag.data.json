{".class": "MypyFile", "_fullname": "transformers.models.rag.retrieval_rag", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BatchEncoding": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.BatchEncoding", "kind": "Gdef", "module_public": false}, "CanonicalHFIndex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.rag.retrieval_rag.HFIndexBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex", "name": "CanonicalHFIndex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.rag.retrieval_rag", "mro": ["transformers.models.rag.retrieval_rag.CanonicalHFIndex", "transformers.models.rag.retrieval_rag.HFIndexBase", "transformers.models.rag.retrieval_rag.Index", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "vector_size", "dataset_name", "dataset_split", "index_name", "index_path", "use_dummy_dataset", "dataset_revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "vector_size", "dataset_name", "dataset_split", "index_name", "index_path", "use_dummy_dataset", "dataset_revision"], "arg_types": ["transformers.models.rag.retrieval_rag.CanonicalHFIndex", "builtins.int", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CanonicalHFIndex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataset_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.dataset_name", "name": "dataset_name", "setter_type": null, "type": "builtins.str"}}, "dataset_revision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.dataset_revision", "name": "dataset_revision", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dataset_split": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.dataset_split", "name": "dataset_split", "setter_type": null, "type": "builtins.str"}}, "index_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.index_name", "name": "index_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "index_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.index_path", "name": "index_path", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "init_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.init_index", "name": "init_index", "type": null}}, "use_dummy_dataset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.use_dummy_dataset", "name": "use_dummy_dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.rag.retrieval_rag.CanonicalHFIndex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.rag.retrieval_rag.CanonicalHFIndex", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CustomHFIndex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.rag.retrieval_rag.HFIndexBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.rag.retrieval_rag.CustomHFIndex", "name": "CustomHFIndex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.CustomHFIndex", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.rag.retrieval_rag", "mro": ["transformers.models.rag.retrieval_rag.CustomHFIndex", "transformers.models.rag.retrieval_rag.HFIndexBase", "transformers.models.rag.retrieval_rag.Index", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "vector_size", "dataset", "index_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.CustomHFIndex.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "vector_size", "dataset", "index_path"], "arg_types": ["transformers.models.rag.retrieval_rag.CustomHFIndex", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CustomHFIndex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.CustomHFIndex.index_path", "name": "index_path", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.CustomHFIndex.init_index", "name": "init_index", "type": null}}, "load_from_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "vector_size", "dataset_path", "index_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.models.rag.retrieval_rag.CustomHFIndex.load_from_disk", "name": "load_from_disk", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.CustomHFIndex.load_from_disk", "name": "load_from_disk", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "vector_size", "dataset_path", "index_path"], "arg_types": [{".class": "TypeType", "item": "transformers.models.rag.retrieval_rag.CustomHFIndex"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_from_disk of CustomHFIndex", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.rag.retrieval_rag.CustomHFIndex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.rag.retrieval_rag.CustomHFIndex", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dataset": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.Dataset", "name": "Dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.rag.retrieval_rag.Dataset", "source_any": null, "type_of_any": 3}}}, "HFIndexBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.rag.retrieval_rag.Index"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase", "name": "HFIndexBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.rag.retrieval_rag", "mro": ["transformers.models.rag.retrieval_rag.HFIndexBase", "transformers.models.rag.retrieval_rag.Index", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "vector_size", "dataset", "index_initialized"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase.__init__", "name": "__init__", "type": null}}, "_check_dataset_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "with_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase._check_dataset_format", "name": "_check_dataset_format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "with_index"], "arg_types": ["transformers.models.rag.retrieval_rag.HFIndexBase", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_dataset_format of HFIndexBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_index_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase._index_initialized", "name": "_index_initialized", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dataset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase.dataset", "name": "dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_doc_dicts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "doc_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase.get_doc_dicts", "name": "get_doc_dicts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "doc_ids"], "arg_types": ["transformers.models.rag.retrieval_rag.HFIndexBase", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_doc_dicts of HFIndexBase", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_top_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "question_hidden_states", "n_docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase.get_top_docs", "name": "get_top_docs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "question_hidden_states", "n_docs"], "arg_types": ["transformers.models.rag.retrieval_rag.HFIndexBase", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_top_docs of HFIndexBase", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase.init_index", "name": "init_index", "type": null}}, "is_initialized": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase.is_initialized", "name": "is_initialized", "type": null}}, "vector_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase.vector_size", "name": "vector_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.rag.retrieval_rag.HFIndexBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.rag.retrieval_rag.HFIndexBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Index": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.rag.retrieval_rag.Index", "name": "Index", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.Index", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.rag.retrieval_rag", "mro": ["transformers.models.rag.retrieval_rag.Index", "builtins.object"], "names": {".class": "SymbolTable", "get_doc_dicts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "doc_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.Index.get_doc_dicts", "name": "get_doc_dicts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "doc_ids"], "arg_types": ["transformers.models.rag.retrieval_rag.Index", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_doc_dicts of Index", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_top_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "question_hidden_states", "n_docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.Index.get_top_docs", "name": "get_top_docs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "question_hidden_states", "n_docs"], "arg_types": ["transformers.models.rag.retrieval_rag.Index", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_top_docs of Index", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.Index.init_index", "name": "init_index", "type": null}}, "is_initialized": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.Index.is_initialized", "name": "is_initialized", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.rag.retrieval_rag.Index.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.rag.retrieval_rag.Index", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "LEGACY_INDEX_PATH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.rag.retrieval_rag.LEGACY_INDEX_PATH", "name": "LEGACY_INDEX_PATH", "setter_type": null, "type": "builtins.str"}}, "LegacyIndex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.rag.retrieval_rag.Index"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex", "name": "LegacyIndex", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.rag.retrieval_rag", "mro": ["transformers.models.rag.retrieval_rag.LegacyIndex", "transformers.models.rag.retrieval_rag.Index", "builtins.object"], "names": {".class": "SymbolTable", "INDEX_FILENAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.INDEX_FILENAME", "name": "INDEX_FILENAME", "setter_type": null, "type": "builtins.str"}}, "PASSAGE_FILENAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.PASSAGE_FILENAME", "name": "PASSAGE_FILENAME", "setter_type": null, "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vector_size", "index_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.__init__", "name": "__init__", "type": null}}, "_deserialize_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex._deserialize_index", "name": "_deserialize_index", "type": null}}, "_index_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex._index_initialized", "name": "_index_initialized", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_load_passages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex._load_passages", "name": "_load_passages", "type": null}}, "_resolve_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "index_path", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex._resolve_path", "name": "_resolve_path", "type": null}}, "get_doc_dicts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "doc_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.get_doc_dicts", "name": "get_doc_dicts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "doc_ids"], "arg_types": ["transformers.models.rag.retrieval_rag.LegacyIndex", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "np.array"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_doc_dicts of LegacyIndex", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_top_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "question_hidden_states", "n_docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.get_top_docs", "name": "get_top_docs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "question_hidden_states", "n_docs"], "arg_types": ["transformers.models.rag.retrieval_rag.LegacyIndex", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_top_docs of LegacyIndex", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.index", "name": "index", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "index_id_to_db_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.index_id_to_db_id", "name": "index_id_to_db_id", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "index_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.index_path", "name": "index_path", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.init_index", "name": "init_index", "type": null}}, "is_initialized": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.is_initialized", "name": "is_initialized", "type": null}}, "passages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.passages", "name": "passages", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vector_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.vector_size", "name": "vector_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.rag.retrieval_rag.LegacyIndex.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.rag.retrieval_rag.LegacyIndex", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils.PreTrainedTokenizer", "kind": "Gdef", "module_public": false}, "RagConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.rag.configuration_rag.RagConfig", "kind": "Gdef", "module_public": false}, "RagRetriever": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.rag.retrieval_rag.RagRetriever", "name": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.rag.retrieval_rag", "mro": ["transformers.models.rag.retrieval_rag.RagRetriever", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "question_input_ids", "question_hidden_states", "prefix", "n_docs", "return_tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "question_input_ids", "question_hidden_states", "prefix", "n_docs", "return_tensors"], "arg_types": ["transformers.models.rag.retrieval_rag.RagRetriever", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of RagRetriever", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "config", "question_encoder_tokenizer", "generator_tokenizer", "index", "init_retrieval"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.__init__", "name": "__init__", "type": null}}, "_build_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever._build_index", "name": "_build_index", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever._build_index", "name": "_build_index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_index of RagRetriever", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_chunk_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "t", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever._chunk_tensor", "name": "_chunk_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "t", "chunk_size"], "arg_types": ["transformers.models.rag.retrieval_rag.RagRetriever", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_chunk_tensor of RagRetriever", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_retrieval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever._init_retrieval", "name": "_init_retrieval", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_main_retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "question_hidden_states", "n_docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever._main_retrieve", "name": "_main_retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "question_hidden_states", "n_docs"], "arg_types": ["transformers.models.rag.retrieval_rag.RagRetriever", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_main_retrieve of RagRetriever", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.batch_size", "name": "batch_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ctx_encoder_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.ctx_encoder_tokenizer", "name": "ctx_encoder_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "from_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["cls", "retriever_name_or_path", "indexed_dataset", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.from_pretrained", "name": "from_pretrained", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.from_pretrained", "name": "from_pretrained", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["cls", "retriever_name_or_path", "indexed_dataset", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.rag.retrieval_rag.RagRetriever"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_pretrained of RagRetriever", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "generator_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.generator_tokenizer", "name": "generator_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.index", "name": "index", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_retrieval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.init_retrieval", "name": "init_retrieval", "type": null}}, "n_docs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.n_docs", "name": "n_docs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "postprocess_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "docs", "input_strings", "prefix", "n_docs", "return_tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.postprocess_docs", "name": "postprocess_docs", "type": null}}, "question_encoder_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.question_encoder_tokenizer", "name": "question_encoder_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "question_hidden_states", "n_docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "question_hidden_states", "n_docs"], "arg_types": ["transformers.models.rag.retrieval_rag.RagRetriever", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "retrieve of Rag<PERSON>etriever", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_tokenized_docs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.return_tokenized_docs", "name": "return_tokenized_docs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "save_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "save_directory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.save_pretrained", "name": "save_pretrained", "type": null}}, "set_ctx_encoder_tokenizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ctx_encoder_tokenizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.set_ctx_encoder_tokenizer", "name": "set_ctx_encoder_tokenizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ctx_encoder_tokenizer"], "arg_types": ["transformers.models.rag.retrieval_rag.RagRetriever", "transformers.tokenization_utils.PreTrainedTokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ctx_encoder_tokenizer of RagRetriever", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.rag.retrieval_rag.RagRetriever.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.rag.retrieval_rag.RagRetriever", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RagTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.models.rag.tokenization_rag.RagTokenizer", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.rag.retrieval_rag.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.rag.retrieval_rag.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.rag.retrieval_rag.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.rag.retrieval_rag.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.rag.retrieval_rag.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.rag.retrieval_rag.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.rag.retrieval_rag.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "cached_file": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.cached_file", "kind": "Gdef", "module_public": false}, "faiss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.faiss", "name": "faiss", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.rag.retrieval_rag.faiss", "source_any": null, "type_of_any": 3}}}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_datasets_available", "kind": "Gdef", "module_public": false}, "is_faiss_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_faiss_available", "kind": "Gdef", "module_public": false}, "load_dataset": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.load_dataset", "name": "load_dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.rag.retrieval_rag.load_dataset", "source_any": null, "type_of_any": 3}}}, "load_from_disk": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.rag.retrieval_rag.load_from_disk", "name": "load_from_disk", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.rag.retrieval_rag.load_from_disk", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.rag.retrieval_rag.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef", "module_public": false}, "requires_backends": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.requires_backends", "kind": "Gdef", "module_public": false}, "strtobool": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.strtobool", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\rag\\retrieval_rag.py"}