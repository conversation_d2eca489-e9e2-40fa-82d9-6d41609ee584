{".class": "MypyFile", "_fullname": "transformers.models.siglip2.processing_siglip2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BatchFeature": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_utils.BatchFeature", "kind": "Gdef", "module_public": false}, "ImageInput": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.ImageInput", "kind": "Gdef", "module_public": false}, "ImagesKwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ImagesKwargs", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTokenizedInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTokenizedInput", "kind": "Gdef", "module_public": false}, "ProcessingKwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ProcessingKwargs", "kind": "Gdef", "module_public": false}, "ProcessorMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ProcessorMixin", "kind": "Gdef", "module_public": false}, "Siglip2ImagesKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2ImagesKwargs", "name": "Siglip2ImagesKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2ImagesKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.siglip2.processing_siglip2", "mro": ["transformers.models.siglip2.processing_siglip2.Siglip2ImagesKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["do_resize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size_divisor", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["crop_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["resample", {".class": "UnionType", "items": ["PIL.Image.Resampling", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_rescale", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["rescale_factor", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_normalize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_mean", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_std", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_pad", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["pad_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_center_crop", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["data_format", {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_data_format", {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["device", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_num_patches", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["patch_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}}}, "Siglip2Processor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.processing_utils.ProcessorMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor", "name": "Siglip2Processor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.siglip2.processing_siglip2", "mro": ["transformers.models.siglip2.processing_siglip2.Siglip2Processor", "transformers.processing_utils.ProcessorMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "images", "text", "audio", "videos", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "images", "text", "audio", "videos", "kwargs"], "arg_types": ["transformers.models.siglip2.processing_siglip2.Siglip2Processor", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.siglip2.processing_siglip2.Siglip2ProcessorKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Siglip2Processor", "ret_type": "transformers.feature_extraction_utils.BatchFeature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "image_processor", "tokenizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.__init__", "name": "__init__", "type": null}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.attributes", "name": "attributes", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "batch_decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.batch_decode", "name": "batch_decode", "type": null}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.decode", "name": "decode", "type": null}}, "image_processor_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.image_processor_class", "name": "image_processor_class", "setter_type": null, "type": "builtins.str"}}, "model_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.model_input_names", "name": "model_input_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.model_input_names", "name": "model_input_names", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.siglip2.processing_siglip2.Siglip2Processor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_input_names of Siglip2Processor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tokenizer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.tokenizer_class", "name": "tokenizer_class", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2Processor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.siglip2.processing_siglip2.Siglip2Processor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Siglip2ProcessorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2ProcessorKwargs", "name": "Siglip2ProcessorKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.siglip2.processing_siglip2.Siglip2ProcessorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.siglip2.processing_siglip2", "mro": ["transformers.models.siglip2.processing_siglip2.Siglip2ProcessorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["return_tensors", {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["sampling_rate", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["raw_speech", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["padding", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.utils.generic.PaddingStrategy"], "uses_pep604_syntax": false}], ["max_length", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["truncation", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.tokenization_utils_base.TruncationStrategy"], "uses_pep604_syntax": false}], ["pad_to_multiple_of", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_attention_mask", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_convert_rgb", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_resize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size_divisor", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["default_to_square", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["resample", {".class": "UnionType", "items": ["PIL.Image.Resampling", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_rescale", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["rescale_factor", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_normalize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_mean", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_std", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_pad", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_center_crop", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["crop_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["data_format", {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_data_format", {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["device", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["pad_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["text_pair", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["text_target", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], ["text_pair_target", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["add_special_tokens", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["stride", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["is_split_into_words", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_token_type_ids", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_overflowing_tokens", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_special_tokens_mask", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_offsets_mapping", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_length", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["verbose", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["padding_side", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["common_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.processing_utils.CommonKwargs"}], ["text_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.processing_utils.TextKwargs"}], ["images_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.siglip2.processing_siglip2.Siglip2ImagesKwargs"}], ["videos_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.processing_utils.VideosKwargs"}], ["audio_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.processing_utils.AudioKwargs"}], ["_defaults", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}]], "readonly_keys": [], "required_keys": []}}}, "TextInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.TextInput", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.Unpack", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.siglip2.processing_siglip2.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.siglip2.processing_siglip2.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.siglip2.processing_siglip2.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.siglip2.processing_siglip2.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.siglip2.processing_siglip2.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.siglip2.processing_siglip2.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.siglip2.processing_siglip2.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\siglip2\\processing_siglip2.py"}