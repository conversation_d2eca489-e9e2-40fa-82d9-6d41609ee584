{"data_mtime": 1749061380, "dep_lines": [7, 10, 7, 8, 9, 7, 7, 4, 7, 2, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 20, 20, 5, 20, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.utils.data.datapipes.iter.sharding", "torch.utils.data.datapipes.utils.common", "torch.utils.data.datapipes.iter", "torch.utils.data.datapipes._decorator", "torch.utils.data.datapipes.datapipe", "torch.utils.data.datapipes", "torch.utils.data", "collections.abc", "torch.utils", "warnings", "collections", "typing", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "torch.utils.data.datapipes._typing", "torch.utils.data.datapipes.utils", "torch.utils.data.dataset"], "hash": "290acb49bfb86dec6e2c8294f9b94ee43e2b5504", "id": "torch.utils.data.datapipes.iter.grouping", "ignore_all": true, "interface_hash": "a5c99426bae48de8890aa4d69fa625a69d673821", "mtime": 1749057391, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\grouping.py", "plugin_data": null, "size": 12676, "suppressed": [], "version_id": "1.16.0"}