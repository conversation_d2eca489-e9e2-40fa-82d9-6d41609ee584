{".class": "MypyFile", "_fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.configuration_auto.AutoConfig", "kind": "Gdef", "module_public": false}, "FLAX_MODEL_MAPPING": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_flax_auto.FLAX_MODEL_MAPPING", "kind": "Gdef", "module_public": false}, "FlaxAutoModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_flax_auto.FlaxAutoModel", "kind": "Gdef", "module_public": false}, "FlaxCLIPOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.models.clip.modeling_flax_clip.FlaxCLIPOutput", "kind": "Gdef", "module_public": false}, "FlaxCLIPVisionModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.clip.modeling_flax_clip.FlaxCLIPVisionModel", "kind": "Gdef", "module_public": false}, "FlaxPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "kind": "Gdef", "module_public": false}, "FlaxVisionTextDualEncoderModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_flax_utils.FlaxPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", "name": "FlaxVisionTextDualEncoderModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder", "mro": ["transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "pixel_values", "attention_mask", "position_ids", "token_type_ids", "params", "dropout_rng", "train", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "pixel_values", "attention_mask", "position_ids", "token_type_ids", "params", "dropout_rng", "train", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jax", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxVisionTextDualEncoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "kwargs"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FlaxVisionTextDualEncoderModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["projection_dim", "logit_scale_init_value", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_vision_text_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "vision_model_name_or_path", "text_model_name_or_path", "model_args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.from_vision_text_pretrained", "name": "from_vision_text_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "vision_model_name_or_path", "text_model_name_or_path", "model_args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_vision_text_pretrained of FlaxVisionTextDualEncoderModel", "ret_type": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.from_vision_text_pretrained", "name": "from_vision_text_pretrained", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 2, 4], "arg_names": ["cls", "vision_model_name_or_path", "text_model_name_or_path", "model_args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_vision_text_pretrained of FlaxVisionTextDualEncoderModel", "ret_type": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_image_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "pixel_values", "params", "dropout_rng", "train"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.get_image_features", "name": "get_image_features", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "pixel_values", "params", "dropout_rng", "train"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jax", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_image_features of FlaxVisionTextDualEncoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_text_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "token_type_ids", "params", "dropout_rng", "train"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.get_text_features", "name": "get_text_features", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "token_type_ids", "params", "dropout_rng", "train"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jax", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_text_features of FlaxVisionTextDualEncoderModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.init_weights", "name": "init_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jax", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FrozenDict", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_weights of FlaxVisionTextDualEncoderModel", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FrozenDict", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxVisionTextDualEncoderModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule", "name": "FlaxVisionTextDualEncoderModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder", "mro": ["transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "pixel_values", "attention_mask", "position_ids", "token_type_ids", "deterministic", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "pixel_values", "attention_mask", "position_ids", "token_type_ids", "deterministic", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxVisionTextDualEncoderModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.config", "name": "config", "setter_type": null, "type": "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jnp", "source_any": null, "type_of_any": 3}}}, "logit_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.logit_scale", "name": "logit_scale", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projection_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.projection_dim", "name": "projection_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.setup", "name": "setup", "type": null}}, "text_embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.text_embed_dim", "name": "text_embed_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "text_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.text_model", "name": "text_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "text_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.text_projection", "name": "text_projection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vision_embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.vision_embed_dim", "name": "vision_embed_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vision_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.vision_model", "name": "vision_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "visual_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.visual_projection", "name": "visual_projection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FlaxVisionTextDualEncoderModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrozenDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FrozenDict", "name": "FrozenDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.FrozenDict", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "VISION_TEXT_DUAL_ENCODER_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.VISION_TEXT_DUAL_ENCODER_INPUTS_DOCSTRING", "name": "VISION_TEXT_DUAL_ENCODER_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "VISION_TEXT_DUAL_ENCODER_MODEL_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.VISION_TEXT_DUAL_ENCODER_MODEL_DOCSTRING", "name": "VISION_TEXT_DUAL_ENCODER_MODEL_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "VISION_TEXT_DUAL_ENCODER_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.VISION_TEXT_DUAL_ENCODER_START_DOCSTRING", "name": "VISION_TEXT_DUAL_ENCODER_START_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "VisionTextDualEncoderConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder.VisionTextDualEncoderConfig", "kind": "Gdef", "module_public": false}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "append_replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.append_replace_return_docstrings", "kind": "Gdef", "module_public": false}, "flatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.flatten_dict", "name": "flatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.flatten_dict", "source_any": null, "type_of_any": 3}}}, "freeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.freeze", "name": "freeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.freeze", "source_any": null, "type_of_any": 3}}}, "jax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jax", "name": "jax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jax", "source_any": null, "type_of_any": 3}}}, "jnp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jnp", "name": "jnp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.jnp", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.nn", "name": "nn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.nn", "source_any": null, "type_of_any": 3}}}, "overwrite_call_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.overwrite_call_docstring", "kind": "Gdef", "module_public": false}, "unflatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.unflatten_dict", "name": "unflatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.unflatten_dict", "source_any": null, "type_of_any": 3}}}, "unfreeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.unfreeze", "name": "unfreeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder.unfreeze", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\modeling_flax_vision_text_dual_encoder.py"}