{"data_mtime": 1749061360, "dep_lines": [15, 28, 29, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30], "dependencies": ["transformers.data.data_collator", "transformers.data.metrics", "transformers.data.processors", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "723d1a5781b0fe690b2433ccb9bf4c70acff211f", "id": "transformers.data", "ignore_all": true, "interface_hash": "67f7d324c4e2b68cb59985b71d9ac4a23f10fe35", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\data\\__init__.py", "plugin_data": null, "size": 1490, "suppressed": [], "version_id": "1.16.0"}