{".class": "MypyFile", "_fullname": "transformers.trainer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ADAPTER_CONFIG_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.peft_utils.ADAPTER_CONFIG_NAME", "kind": "Gdef"}, "ADAPTER_SAFE_WEIGHTS_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.peft_utils.ADAPTER_SAFE_WEIGHTS_NAME", "kind": "Gdef"}, "ADAPTER_WEIGHTS_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.peft_utils.ADAPTER_WEIGHTS_NAME", "kind": "Gdef"}, "ALL_HYPERPARAMETER_SEARCH_BACKENDS": {".class": "SymbolTableNode", "cross_ref": "transformers.hyperparameter_search.ALL_HYPERPARAMETER_SEARCH_BACKENDS", "kind": "Gdef"}, "ALL_LAYERNORM_LAYERS": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.ALL_LAYERNORM_LAYERS", "kind": "Gdef"}, "Accelerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.Accelerator", "name": "Accelerator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.Accelerator", "source_any": null, "type_of_any": 3}}}, "AcceleratorState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.AcceleratorState", "name": "AcceleratorState", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.AcceleratorState", "source_any": null, "type_of_any": 3}}}, "Adafactor": {".class": "SymbolTableNode", "cross_ref": "transformers.optimization.Adafactor", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutocastKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.AutocastKwargs", "name": "AutocastKwargs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.AutocastKwargs", "source_any": null, "type_of_any": 3}}}, "BaseImageProcessor": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_utils.BaseImageProcessor", "kind": "Gdef"}, "BestRun": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.BestRun", "kind": "Gdef"}, "CONFIG_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.CONFIG_NAME", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CallbackHandler": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.CallbackHandler", "kind": "Gdef"}, "DATA_SAMPLERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.DATA_SAMPLERS", "name": "DATA_SAMPLERS", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["data_source", "replacement", "num_samples", "generator"], "arg_types": ["typing.Sized", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["torch.utils.data.sampler.RandomSampler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RandomSampler", "ret_type": "torch.utils.data.sampler.RandomSampler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "DEFAULT_CALLBACKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.DEFAULT_CALLBACKS", "name": "DEFAULT_CALLBACKS", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["transformers.trainer_callback.DefaultFlowCallback"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DefaultFlowCallback", "ret_type": "transformers.trainer_callback.DefaultFlowCallback", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "DEFAULT_PROGRESS_CALLBACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "transformers.trainer.DEFAULT_PROGRESS_CALLBACK", "line": 187, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "transformers.trainer_callback.ProgressCallback"}}, "DataCollator": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollator", "kind": "Gdef"}, "DataCollatorWithPadding": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollatorWithPadding", "kind": "Gdef"}, "DataLoader": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.dataloader.DataLoader", "kind": "Gdef"}, "DataLoaderConfiguration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.DataLoaderConfiguration", "name": "DataLoaderConfiguration", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.DataLoaderConfiguration", "source_any": null, "type_of_any": 3}}}, "Dataset": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.dataset.Dataset", "kind": "Gdef"}, "DebugOption": {".class": "SymbolTableNode", "cross_ref": "transformers.debug_utils.DebugOption", "kind": "Gdef"}, "DebugUnderflowOverflow": {".class": "SymbolTableNode", "cross_ref": "transformers.debug_utils.DebugUnderflowOverflow", "kind": "Gdef"}, "DeepSpeedSchedulerWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.DeepSpeedSchedulerWrapper", "name": "DeepSpeedSchedulerWrapper", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.DeepSpeedSchedulerWrapper", "source_any": null, "type_of_any": 3}}}, "DefaultFlowCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.DefaultFlowCallback", "kind": "Gdef"}, "DistributedDataParallelKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.DistributedDataParallelKwargs", "name": "DistributedDataParallelKwargs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.DistributedDataParallelKwargs", "source_any": null, "type_of_any": 3}}}, "DistributedTensorGatherer": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.DistributedTensorGatherer", "kind": "Gdef"}, "DistributedType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.DistributedType", "name": "DistributedType", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.DistributedType", "source_any": null, "type_of_any": 3}}}, "EvalLoopContainer": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.EvalLoopContainer", "kind": "Gdef"}, "EvalLoopOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.EvalLoopOutput", "kind": "Gdef"}, "EvalPrediction": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.EvalPrediction", "kind": "Gdef"}, "ExportableState": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.ExportableState", "kind": "Gdef"}, "FSDP_MODEL_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.FSDP_MODEL_NAME", "name": "FSDP_MODEL_NAME", "setter_type": null, "type": "builtins.str"}}, "FeatureExtractionMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_utils.FeatureExtractionMixin", "kind": "Gdef"}, "HPSearchBackend": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.HPSearchBackend", "kind": "Gdef"}, "HubStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.HubStrategy", "kind": "Gdef"}, "IS_SAGEMAKER_MP_POST_1_10": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.IS_SAGEMAKER_MP_POST_1_10", "name": "IS_SAGEMAKER_MP_POST_1_10", "setter_type": null, "type": "builtins.bool"}}, "IS_XLA_FSDPV2_POST_2_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.IS_XLA_FSDPV2_POST_2_2", "name": "IS_XLA_FSDPV2_POST_2_2", "setter_type": null, "type": "builtins.bool"}}, "IterableDataset": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.dataset.IterableDataset", "kind": "Gdef"}, "IterableDatasetShard": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.IterableDatasetShard", "kind": "Gdef"}, "LabelSmoother": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.LabelSmoother", "kind": "Gdef"}, "LayerWiseDummyOptimizer": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.LayerWiseDummyOptimizer", "kind": "Gdef"}, "LengthGroupedSampler": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.LengthGroupedSampler", "kind": "Gdef"}, "MODEL_FOR_CAUSAL_LM_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.MODEL_FOR_CAUSAL_LM_MAPPING_NAMES", "kind": "Gdef"}, "MODEL_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.MODEL_MAPPING_NAMES", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "ModelCard": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.repocard.ModelCard", "kind": "Gdef"}, "NotebookProgressCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.notebook.NotebookProgressCallback", "kind": "Gdef"}, "OPTIMIZER_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.OPTIMIZER_NAME", "name": "OPTIMIZER_NAME", "setter_type": null, "type": "builtins.str"}}, "OPTIMIZER_NAME_BIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.OPTIMIZER_NAME_BIN", "name": "OPTIMIZER_NAME_BIN", "setter_type": null, "type": "builtins.str"}}, "OptimizerNames": {".class": "SymbolTableNode", "cross_ref": "transformers.training_args.OptimizerNames", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PREFIX_CHECKPOINT_DIR": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.PREFIX_CHECKPOINT_DIR", "kind": "Gdef"}, "ParallelMode": {".class": "SymbolTableNode", "cross_ref": "transformers.training_args.ParallelMode", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PeftModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.PeftModel", "name": "PeftModel", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.PeftModel", "source_any": null, "type_of_any": 3}}}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef"}, "PreTrainedTokenizerBase": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "kind": "Gdef"}, "PredictionOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.PredictionOutput", "kind": "Gdef"}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef"}, "PrinterCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.PrinterCallback", "kind": "Gdef"}, "ProcessorMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ProcessorMixin", "kind": "Gdef"}, "ProgressCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.ProgressCallback", "kind": "Gdef"}, "PushInProgress": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.PushInProgress", "kind": "Gdef"}, "PushToHubMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.PushToHubMixin", "kind": "Gdef"}, "QuantizationMethod": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.QuantizationMethod", "kind": "Gdef"}, "RandomSampler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.sampler.RandomSampler", "kind": "Gdef"}, "RemoveColumnsCollator": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.RemoveColumnsCollator", "kind": "Gdef"}, "SAFE_WEIGHTS_INDEX_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.SAFE_WEIGHTS_INDEX_NAME", "kind": "Gdef"}, "SAFE_WEIGHTS_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.SAFE_WEIGHTS_NAME", "kind": "Gdef"}, "SCALER_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.SCALER_NAME", "name": "SCALER_NAME", "setter_type": null, "type": "builtins.str"}}, "SCHEDULER_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.SCHEDULER_NAME", "name": "SCHEDULER_NAME", "setter_type": null, "type": "builtins.str"}}, "SMP_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.SMP_VERSION", "name": "SMP_VERSION", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.SMP_VERSION", "source_any": null, "type_of_any": 3}}}, "SaveStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.SaveStrategy", "kind": "Gdef"}, "SeedableRandomSampler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.SeedableRandomSampler", "name": "SeedableRandomSampler", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.SeedableRandomSampler", "source_any": null, "type_of_any": 3}}}, "SequenceFeatureExtractor": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_sequence_utils.SequenceFeatureExtractor", "kind": "Gdef"}, "SequentialDistributedSampler": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.SequentialDistributedSampler", "kind": "Gdef"}, "SequentialSampler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.sampler.SequentialSampler", "kind": "Gdef"}, "TRAINER_STATE_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.TRAINER_STATE_NAME", "name": "TRAINER_STATE_NAME", "setter_type": null, "type": "builtins.str"}}, "TRAINING_ARGS_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.TRAINING_ARGS_NAME", "name": "TRAINING_ARGS_NAME", "setter_type": null, "type": "builtins.str"}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TorchTensorParallelPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.TorchTensorParallelPlugin", "name": "TorchTensorParallelPlugin", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.TorchTensorParallelPlugin", "source_any": null, "type_of_any": 3}}}, "TrainOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.TrainOutput", "kind": "Gdef"}, "Trainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.trainer.Trainer", "name": "Trainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.trainer", "mro": ["transformers.trainer.Trainer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "args", "data_collator", "train_dataset", "eval_dataset", "processing_class", "model_init", "compute_loss_func", "compute_metrics", "callbacks", "optimizers", "optimizer_cls_and_kwargs", "preprocess_logits_for_metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.trainer.Trainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "args", "data_collator", "train_dataset", "eval_dataset", "processing_class", "model_init", "compute_loss_func", "compute_metrics", "callbacks", "optimizers", "optimizer_cls_and_kwargs", "preprocess_logits_for_metrics"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", {".class": "NoneType"}], "uses_pep604_syntax": false}, "transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["transformers.data.data_collator.DataCollator", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.IterableDataset"}, {".class": "AnyType", "missing_import_name": "transformers.trainer.datasets", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "transformers.trainer.datasets", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase", "transformers.image_processing_utils.BaseImageProcessor", "transformers.feature_extraction_utils.FeatureExtractionMixin", "transformers.processing_utils.ProcessorMixin", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.modeling_utils.PreTrainedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.trainer_utils.EvalPrediction"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_callback.TrainerCallback"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.optim.lr_scheduler.LambdaLR", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "torch.optim.optimizer.Optimizer"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Trainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.trainer.Trainer.__init__", "name": "__init__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "_activate_neftune": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._activate_neftune", "name": "_activate_neftune", "type": null}}, "_add_sm_patterns_to_gitignore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._add_sm_patterns_to_gitignore", "name": "_add_sm_patterns_to_gitignore", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.trainer.Trainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_sm_patterns_to_gitignore of Trainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_created_lr_scheduler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._created_lr_scheduler", "name": "_created_lr_scheduler", "setter_type": null, "type": "builtins.bool"}}, "_deactivate_neftune": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._deactivate_neftune", "name": "_deactivate_neftune", "type": null}}, "_determine_best_metric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "metrics", "trial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._determine_best_metric", "name": "_determine_best_metric", "type": null}}, "_eval_dataloaders": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._eval_dataloaders", "name": "_eval_dataloaders", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "trial", "ignore_keys_for_eval", "skip_scheduler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._evaluate", "name": "_evaluate", "type": null}}, "_finish_current_push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._finish_current_push", "name": "_finish_current_push", "type": null}}, "_fsdp_qlora_plugin_updates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._fsdp_qlora_plugin_updates", "name": "_fsdp_qlora_plugin_updates", "type": null}}, "_gather_and_numpify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._gather_and_numpify", "name": "_gather_and_numpify", "type": null}}, "_get_collator_with_removed_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "data_collator", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._get_collator_with_removed_columns", "name": "_get_collator_with_removed_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "data_collator", "description"], "arg_types": ["transformers.trainer.Trainer", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_collator_with_removed_columns of Trainer", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_dataloader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataset", "description", "batch_size", "sampler_fn", "is_training", "dataloader_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._get_dataloader", "name": "_get_dataloader", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataset", "description", "batch_size", "sampler_fn", "is_training", "dataloader_key"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.sampler.Sampler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_dataloader of Trainer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_eval_sampler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "eval_dataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._get_eval_sampler", "name": "_get_eval_sampler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "eval_dataset"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_eval_sampler of Trainer", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.sampler.Sampler"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_learning_rate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.Trainer._get_learning_rate", "name": "_get_learning_rate", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "_get_output_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "trial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._get_output_dir", "name": "_get_output_dir", "type": null}}, "_get_train_sampler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "train_dataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._get_train_sampler", "name": "_get_train_sampler", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "train_dataset"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_train_sampler of Trainer", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.sampler.Sampler"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_globalstep_last_logged": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._globalstep_last_logged", "name": "_globalstep_last_logged", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_hp_search_setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "trial"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._hp_search_setup", "name": "_hp_search_setup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "trial"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.trainer.optuna", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_hp_search_setup of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_inner_training_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "batch_size", "args", "resume_from_checkpoint", "trial", "ignore_keys_for_eval"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._inner_training_loop", "name": "_inner_training_loop", "type": null}}, "_issue_warnings_after_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._issue_warnings_after_load", "name": "_issue_warnings_after_load", "type": null}}, "_load_best_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._load_best_model", "name": "_load_best_model", "type": null}}, "_load_callback_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._load_callback_state", "name": "_load_callback_state", "type": null}}, "_load_from_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "resume_from_checkpoint", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._load_from_checkpoint", "name": "_load_from_checkpoint", "type": null}}, "_load_optimizer_and_scheduler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._load_optimizer_and_scheduler", "name": "_load_optimizer_and_scheduler", "type": null}}, "_load_rng_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._load_rng_state", "name": "_load_rng_state", "type": null}}, "_load_scaler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._load_scaler", "name": "_load_scaler", "type": null}}, "_loggers_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._loggers_initialized", "name": "_loggers_initialized", "setter_type": null, "type": "builtins.bool"}}, "_maybe_log_save_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "tr_loss", "grad_norm", "model", "trial", "epoch", "ignore_keys_for_eval", "start_time", "learning_rate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._maybe_log_save_evaluate", "name": "_maybe_log_save_evaluate", "type": null}}, "_memory_tracker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._memory_tracker", "name": "_memory_tracker", "setter_type": null, "type": "transformers.trainer_utils.TrainerMemoryTracker"}}, "_move_model_to_device": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._move_model_to_device", "name": "_move_model_to_device", "type": null}}, "_nested_gather": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tensors", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._nested_gather", "name": "_nested_gather", "type": null}}, "_past": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._past", "name": "_past", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_prepare_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._prepare_input", "name": "_prepare_input", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_input of Trainer", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._prepare_inputs", "name": "_prepare_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_inputs of Trainer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_push_from_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_folder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._push_from_checkpoint", "name": "_push_from_checkpoint", "type": null}}, "_remove_unused_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._remove_unused_columns", "name": "_remove_unused_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset", "description"], "arg_types": ["transformers.trainer.Trainer", {".class": "AnyType", "missing_import_name": "transformers.trainer.datasets", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_unused_columns of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_report_to_hp_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "trial", "step", "metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._report_to_hp_search", "name": "_report_to_hp_search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "trial", "step", "metrics"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.trainer.optuna", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_report_to_hp_search of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rotate_checkpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "use_mtime", "output_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._rotate_checkpoints", "name": "_rotate_checkpoints", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "use_mtime", "output_dir"], "arg_types": ["transformers.trainer.Trainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rotate_checkpoints of Trainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "output_dir", "state_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._save", "name": "_save", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "output_dir", "state_dict"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_save of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_save_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "trial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._save_checkpoint", "name": "_save_checkpoint", "type": null}}, "_save_optimizer_and_scheduler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._save_optimizer_and_scheduler", "name": "_save_optimizer_and_scheduler", "type": null}}, "_save_rng_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._save_rng_state", "name": "_save_rng_state", "type": null}}, "_save_scaler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._save_scaler", "name": "_save_scaler", "type": null}}, "_save_tpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "output_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._save_tpu", "name": "_save_tpu", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "output_dir"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_save_tpu of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_signature_columns_if_needed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._set_signature_columns_if_needed", "name": "_set_signature_columns_if_needed", "type": null}}, "_signature_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._signature_columns", "name": "_signature_columns", "setter_type": null, "type": {".class": "NoneType"}}}, "_sorted_checkpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "output_dir", "checkpoint_prefix", "use_mtime"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._sorted_checkpoints", "name": "_sorted_checkpoints", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "output_dir", "checkpoint_prefix", "use_mtime"], "arg_types": ["transformers.trainer.Trainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sorted_checkpoints of <PERSON>er", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_total_loss_scalar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._total_loss_scalar", "name": "_total_loss_scalar", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_train_batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._train_batch_size", "name": "_train_batch_size", "setter_type": null, "type": "builtins.int"}}, "_trial": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer._trial", "name": "_trial", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.trainer.optuna", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "_tune_save_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer._tune_save_checkpoint", "name": "_tune_save_checkpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_dir"], "arg_types": ["transformers.trainer.Trainer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tune_save_checkpoint of <PERSON>er", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrap_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "training", "dataloader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer._wrap_model", "name": "_wrap_model", "type": null}}, "accelerator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.accelerator", "name": "accelerator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "add_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.add_callback", "name": "add_callback", "type": null}}, "amp_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.amp_dtype", "name": "amp_dtype", "setter_type": null, "type": "torch._C.dtype"}}, "args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.args", "name": "args", "setter_type": null, "type": "transformers.training_args.TrainingArguments"}}, "autocast_smart_context_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "cache_enabled"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.autocast_smart_context_manager", "name": "autocast_smart_context_manager", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "cache_enabled"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autocast_smart_context_manager of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_model_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "trial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.call_model_init", "name": "call_model_init", "type": null}}, "callback_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.callback_handler", "name": "callback_handler", "setter_type": null, "type": "transformers.trainer_callback.CallbackHandler"}}, "can_return_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.can_return_loss", "name": "can_return_loss", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "compare_trainer_and_checkpoint_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "training_args", "trainer_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.compare_trainer_and_checkpoint_args", "name": "compare_trainer_and_checkpoint_args", "type": null}}, "compute_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "model", "inputs", "return_outputs", "num_items_in_batch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.compute_loss", "name": "compute_loss", "type": null}}, "compute_loss_context_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.compute_loss_context_manager", "name": "compute_loss_context_manager", "type": null}}, "compute_loss_func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.compute_loss_func", "name": "compute_loss_func", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "compute_metrics": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.compute_metrics", "name": "compute_metrics", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.trainer_utils.EvalPrediction"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "compute_objective": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.compute_objective", "name": "compute_objective", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "control": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.control", "name": "control", "setter_type": null, "type": "transformers.trainer_callback.TrainerControl"}}, "create_accelerator_and_postprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.create_accelerator_and_postprocess", "name": "create_accelerator_and_postprocess", "type": null}}, "create_model_card": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "language", "license", "tags", "model_name", "finetuned_from", "tasks", "dataset_tags", "dataset", "dataset_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.create_model_card", "name": "create_model_card", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "language", "license", "tags", "model_name", "finetuned_from", "tasks", "dataset_tags", "dataset", "dataset_args"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_model_card of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_optimizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.create_optimizer", "name": "create_optimizer", "type": null}}, "create_optimizer_and_scheduler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "num_training_steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.create_optimizer_and_scheduler", "name": "create_optimizer_and_scheduler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "num_training_steps"], "arg_types": ["transformers.trainer.Trainer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_optimizer_and_scheduler of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_scheduler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "num_training_steps", "optimizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.create_scheduler", "name": "create_scheduler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "num_training_steps", "optimizer"], "arg_types": ["transformers.trainer.Trainer", "builtins.int", "torch.optim.optimizer.Optimizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_scheduler of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_flos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.current_flos", "name": "current_flos", "setter_type": null, "type": "builtins.int"}}, "data_collator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.data_collator", "name": "data_collator", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.data.data_collator.DataCollator", "transformers.data.data_collator.DataCollatorWithPadding", {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["features", "return_tensors"], "arg_types": [{".class": "Instance", "args": ["transformers.data.data_collator.InputDataClass"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_data_collator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "deepspeed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.deepspeed", "name": "deepspeed", "setter_type": null, "type": {".class": "NoneType"}}}, "eval_dataset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.eval_dataset", "name": "eval_dataset", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "transformers.trainer.datasets", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "eval_dataset", "ignore_keys", "metric_key_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.evaluate", "name": "evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "eval_dataset", "ignore_keys", "metric_key_prefix"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "evaluate of Trainer", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evaluation_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataloader", "description", "prediction_loss_only", "ignore_keys", "metric_key_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.evaluation_loop", "name": "evaluation_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataloader", "description", "prediction_loss_only", "ignore_keys", "metric_key_prefix"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "evaluation_loop of Trainer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.EvalLoopOutput"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "floating_point_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.floating_point_ops", "name": "floating_point_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "floating_point_ops of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gather_function": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.gather_function", "name": "gather_function", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "get_batch_samples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "epoch_iterator", "num_batches", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.get_batch_samples", "name": "get_batch_samples", "type": null}}, "get_decay_parameter_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.get_decay_parameter_names", "name": "get_decay_parameter_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["transformers.trainer.Trainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_decay_parameter_names of Trainer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_eval_dataloader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_dataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.get_eval_dataloader", "name": "get_eval_dataloader", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_dataset"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_eval_dataloader of Trainer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_learning_rates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.get_learning_rates", "name": "get_learning_rates", "type": null}}, "get_num_trainable_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.get_num_trainable_parameters", "name": "get_num_trainable_parameters", "type": null}}, "get_optimizer_cls_and_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["args", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.trainer.Trainer.get_optimizer_cls_and_kwargs", "name": "get_optimizer_cls_and_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["args", "model"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["transformers.modeling_utils.PreTrainedModel", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_optimizer_cls_and_kwargs of Trainer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.trainer.Trainer.get_optimizer_cls_and_kwargs", "name": "get_optimizer_cls_and_kwargs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["args", "model"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["transformers.modeling_utils.PreTrainedModel", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_optimizer_cls_and_kwargs of Trainer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_optimizer_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "param"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.get_optimizer_group", "name": "get_optimizer_group", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "param"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", "torch.nn.parameter.Parameter", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_optimizer_group of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_test_dataloader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "test_dataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.get_test_dataloader", "name": "get_test_dataloader", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "test_dataset"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_test_dataloader of Trainer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_train_dataloader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.get_train_dataloader", "name": "get_train_dataloader", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.trainer.Trainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_train_dataloader of Trainer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hp_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.hp_name", "name": "hp_name", "setter_type": null, "type": {".class": "NoneType"}}}, "hp_search_backend": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.hp_search_backend", "name": "hp_search_backend", "setter_type": null, "type": {".class": "NoneType"}}}, "hp_space": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.hp_space", "name": "hp_space", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["trial"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.hyperparameter_search.HyperParamSearchBackendBase"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_hp_space", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.trainer.optuna", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "hub_model_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.hub_model_id", "name": "hub_model_id", "setter_type": null, "type": {".class": "NoneType"}}}, "hyperparameter_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "hp_space", "compute_objective", "n_trials", "direction", "backend", "hp_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.hyperparameter_search", "name": "hyperparameter_search", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "hp_space", "compute_objective", "n_trials", "direction", "backend", "hp_name", "kwargs"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.trainer.optuna", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.trainer_utils.HPSearchBackend", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.trainer.optuna", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hyperparameter_search of Trainer", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.BestRun"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.BestRun"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_hf_repo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.init_hf_repo", "name": "init_hf_repo", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "token"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_hf_repo of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ipex_optimize_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "training", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.ipex_optimize_model", "name": "ipex_optimize_model", "type": null}}, "is_deepspeed_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.is_deepspeed_enabled", "name": "is_deepspeed_enabled", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_fsdp_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.is_fsdp_enabled", "name": "is_fsdp_enabled", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_fsdp_xla_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.is_fsdp_xla_enabled", "name": "is_fsdp_xla_enabled", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "builtins.str"], "uses_pep604_syntax": false}}}, "is_fsdp_xla_v1_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.is_fsdp_xla_v1_enabled", "name": "is_fsdp_xla_v1_enabled", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "LiteralType", "fallback": "builtins.str", "value": ""}, "builtins.bool"], "uses_pep604_syntax": false}}}, "is_fsdp_xla_v2_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.is_fsdp_xla_v2_enabled", "name": "is_fsdp_xla_v2_enabled", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "is_in_train": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.is_in_train", "name": "is_in_train", "setter_type": null, "type": "builtins.bool"}}, "is_local_process_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.is_local_process_zero", "name": "is_local_process_zero", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.trainer.Trainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_local_process_zero of Trainer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_model_parallel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.is_model_parallel", "name": "is_model_parallel", "setter_type": null, "type": "builtins.bool"}}, "is_tp_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.is_tp_enabled", "name": "is_tp_enabled", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_world_process_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.is_world_process_zero", "name": "is_world_process_zero", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.trainer.Trainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_world_process_zero of <PERSON>er", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "jit_compilation_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.jit_compilation_time", "name": "jit_compilation_time", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "label_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.label_names", "name": "label_names", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}}, "label_smoother": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.label_smoother", "name": "label_smoother", "setter_type": null, "type": "transformers.trainer_pt_utils.LabelSmoother"}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "logs", "start_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "logs", "start_time"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of Trainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log_metrics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.Trainer.log_metrics", "name": "log_metrics", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "lr_scheduler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.lr_scheduler", "name": "lr_scheduler", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.optim.lr_scheduler.LambdaLR", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "metrics_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.Trainer.metrics_format", "name": "metrics_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.model", "name": "model", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "model_accepts_loss_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.model_accepts_loss_kwargs", "name": "model_accepts_loss_kwargs", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "torch._tensor.Tensor", "torch.nn.modules.module.Module"], "uses_pep604_syntax": false}}}, "model_init": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.model_init", "name": "model_init", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.modeling_utils.PreTrainedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_preparation_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.model_preparation_time", "name": "model_preparation_time", "setter_type": null, "type": "builtins.float"}}, "model_wrapped": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.model_wrapped", "name": "model_wrapped", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "torch.nn.modules.module.Module"], "uses_pep604_syntax": false}}}, "neftune_hook_handle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.neftune_hook_handle", "name": "neftune_hook_handle", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "neftune_noise_alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.neftune_noise_alpha", "name": "neftune_noise_alpha", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "num_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dataloader"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.num_examples", "name": "num_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dataloader"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "num_examples of Trainer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "num_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["train_dl", "max_steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.trainer.Trainer.num_tokens", "name": "num_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["train_dl", "max_steps"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "num_tokens of Trainer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.trainer.Trainer.num_tokens", "name": "num_tokens", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["train_dl", "max_steps"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "num_tokens of Trainer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "objective": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.objective", "name": "objective", "setter_type": null, "type": null}}, "optimizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.optimizer", "name": "optimizer", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.optim.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "optimizer_cls_and_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.optimizer_cls_and_kwargs", "name": "optimizer_cls_and_kwargs", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "torch.optim.optimizer.Optimizer"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "place_model_on_device": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.place_model_on_device", "name": "place_model_on_device", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pop_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.pop_callback", "name": "pop_callback", "type": null}}, "predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "test_dataset", "ignore_keys", "metric_key_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "test_dataset", "ignore_keys", "metric_key_prefix"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of Trainer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.PredictionOutput"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prediction_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataloader", "description", "prediction_loss_only", "ignore_keys", "metric_key_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.prediction_loop", "name": "prediction_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataloader", "description", "prediction_loss_only", "ignore_keys", "metric_key_prefix"], "arg_types": ["transformers.trainer.Trainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prediction_loop of Trainer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.EvalLoopOutput"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prediction_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "model", "inputs", "prediction_loss_only", "ignore_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.prediction_step", "name": "prediction_step", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "model", "inputs", "prediction_loss_only", "ignore_keys"], "arg_types": ["transformers.trainer.Trainer", "torch.nn.modules.module.Module", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prediction_step of Trainer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preprocess_logits_for_metrics": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.preprocess_logits_for_metrics", "name": "preprocess_logits_for_metrics", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "processing_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.processing_class", "name": "processing_class", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase", "transformers.image_processing_utils.BaseImageProcessor", "transformers.feature_extraction_utils.FeatureExtractionMixin", "transformers.processing_utils.ProcessorMixin", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "propagate_args_to_deepspeed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "auto_find_batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.propagate_args_to_deepspeed", "name": "propagate_args_to_deepspeed", "type": null}}, "push_in_progress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.push_in_progress", "name": "push_in_progress", "setter_type": null, "type": {".class": "NoneType"}}}, "push_to_hub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "commit_message", "blocking", "token", "revision", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.push_to_hub", "name": "push_to_hub", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "commit_message", "blocking", "token", "revision", "kwargs"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_to_hub of Trainer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.remove_callback", "name": "remove_callback", "type": null}}, "save_metrics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.Trainer.save_metrics", "name": "save_metrics", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "save_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "output_dir", "_internal_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.save_model", "name": "save_model", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "output_dir", "_internal_call"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_model of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.Trainer.save_state", "name": "save_state", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "set_initial_training_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "args", "dataloader", "total_train_batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.set_initial_training_values", "name": "set_initial_training_values", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "args", "dataloader", "total_train_batch_size"], "arg_types": ["transformers.trainer.Trainer", "transformers.training_args.TrainingArguments", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_initial_training_values of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.state", "name": "state", "setter_type": null, "type": "transformers.trainer_callback.TrainerState"}}, "store_flos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.store_flos", "name": "store_flos", "type": null}}, "tokenizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "transformers.trainer.Trainer.tokenizer", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated", "is_trivial_self"], "fullname": "transformers.trainer.Trainer.tokenizer", "name": "tokenizer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.trainer.Trainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tokenizer of Trainer", "ret_type": {".class": "UnionType", "items": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "transformers.trainer.Trainer.tokenizer", "name": "tokenizer", "setter_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "processing_class"], "arg_types": ["transformers.trainer.Trainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tokenizer of Trainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.trainer.Trainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tokenizer of Trainer", "ret_type": {".class": "UnionType", "items": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "processing_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.trainer.Trainer.tokenizer", "name": "tokenizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "processing_class"], "arg_types": ["transformers.trainer.Trainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tokenizer of Trainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "", "name": "tokenizer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "processing_class"], "arg_types": ["transformers.trainer.Trainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tokenizer of Trainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.trainer.Trainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tokenizer of Trainer", "ret_type": {".class": "UnionType", "items": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "torch_jit_model_eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "dataloader", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.Trainer.torch_jit_model_eval", "name": "torch_jit_model_eval", "type": null}}, "train": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "resume_from_checkpoint", "trial", "ignore_keys_for_eval", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "resume_from_checkpoint", "trial", "ignore_keys_for_eval", "kwargs"], "arg_types": ["transformers.trainer.Trainer", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.trainer.optuna", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of Trainer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "train_dataset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.train_dataset", "name": "train_dataset", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.IterableDataset"}, {".class": "AnyType", "missing_import_name": "transformers.trainer.datasets", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "training_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "inputs", "num_items_in_batch"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.trainer.Trainer.training_step", "name": "training_step", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "inputs", "num_items_in_batch"], "arg_types": ["transformers.trainer.Trainer", "torch.nn.modules.module.Module", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "training_step of Trainer", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_apex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.use_apex", "name": "use_apex", "setter_type": null, "type": "builtins.bool"}}, "use_cpu_amp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.trainer.Trainer.use_cpu_amp", "name": "use_cpu_amp", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.trainer.Trainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.trainer.Trainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "TrainerControl": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerControl", "kind": "Gdef"}, "TrainerMemoryTracker": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.TrainerMemoryTracker", "kind": "Gdef"}, "TrainerState": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerState", "kind": "Gdef"}, "TrainingArguments": {".class": "SymbolTableNode", "cross_ref": "transformers.training_args.TrainingArguments", "kind": "Gdef"}, "TrainingSummary": {".class": "SymbolTableNode", "cross_ref": "transformers.modelcard.TrainingSummary", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WEIGHTS_INDEX_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.WEIGHTS_INDEX_NAME", "kind": "Gdef"}, "WEIGHTS_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.WEIGHTS_NAME", "kind": "Gdef"}, "XLA_FSDPV2_MIN_VERSION": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.XLA_FSDPV2_MIN_VERSION", "kind": "Gdef"}, "XLA_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.XLA_VERSION", "name": "XLA_VERSION", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.XLA_VERSION", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.trainer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "transformers.__version__", "kind": "Gdef"}, "_get_fsdp_ckpt_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer._get_fsdp_ckpt_kwargs", "name": "_get_fsdp_ckpt_kwargs", "type": null}}, "_is_peft_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer._is_peft_model", "name": "_is_peft_model", "type": null}}, "accelerate_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.accelerate_version", "name": "accelerate_version", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.accelerate_version", "source_any": null, "type_of_any": 3}}}, "amp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.amp", "name": "amp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.amp", "source_any": null, "type_of_any": 3}}}, "can_return_loss": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.can_return_loss", "kind": "Gdef"}, "check_target_module_exists": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.check_target_module_exists", "kind": "Gdef"}, "check_torch_load_is_safe": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.check_torch_load_is_safe", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "create_repo": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.hf_api.create_repo", "kind": "Gdef"}, "datasets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.datasets", "name": "datasets", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.datasets", "source_any": null, "type_of_any": 3}}}, "deepspeed_init": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.deepspeed.deepspeed_init", "kind": "Gdef"}, "deepspeed_load_checkpoint": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.deepspeed.deepspeed_load_checkpoint", "kind": "Gdef"}, "default_compute_objective": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.default_compute_objective", "kind": "Gdef"}, "default_data_collator": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.default_data_collator", "kind": "Gdef"}, "default_hp_search_backend": {".class": "SymbolTableNode", "cross_ref": "transformers.hyperparameter_search.default_hp_search_backend", "kind": "Gdef"}, "denumpify_detensorize": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.denumpify_detensorize", "kind": "Gdef"}, "deprecate_kwarg": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.deprecation.deprecate_kwarg", "kind": "Gdef"}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "distributed_broadcast_scalars": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.distributed_broadcast_scalars", "kind": "Gdef"}, "distributed_concat": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.distributed_concat", "kind": "Gdef"}, "enable_full_determinism": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.enable_full_determinism", "kind": "Gdef"}, "find_batch_size": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.find_batch_size", "kind": "Gdef"}, "find_executable_batch_size": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.find_executable_batch_size", "kind": "Gdef"}, "find_labels": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.find_labels", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_last_checkpoint": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.get_last_checkpoint", "kind": "Gdef"}, "get_model_param_count": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.get_model_param_count", "kind": "Gdef"}, "get_module_class_from_name": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.get_module_class_from_name", "kind": "Gdef"}, "get_parameter_names": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.get_parameter_names", "kind": "Gdef"}, "get_reporting_integration_callbacks": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.integration_utils.get_reporting_integration_callbacks", "kind": "Gdef"}, "get_scheduler": {".class": "SymbolTableNode", "cross_ref": "transformers.optimization.get_scheduler", "kind": "Gdef"}, "glob": {".class": "SymbolTableNode", "cross_ref": "glob", "kind": "Gdef"}, "has_length": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.has_length", "kind": "Gdef"}, "hf_hub_utils": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.utils", "kind": "Gdef"}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_accelerate_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_accelerate_available", "kind": "Gdef"}, "is_apex_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_apex_available", "kind": "Gdef"}, "is_apollo_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_apollo_torch_available", "kind": "Gdef"}, "is_bitsandbytes_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_bitsandbytes_available", "kind": "Gdef"}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_datasets_available", "kind": "Gdef"}, "is_deepspeed_available": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.deepspeed.is_deepspeed_available", "kind": "Gdef"}, "is_galore_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_galore_torch_available", "kind": "Gdef"}, "is_grokadamw_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_grokadamw_available", "kind": "Gdef"}, "is_in_notebook": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_in_notebook", "kind": "Gdef"}, "is_ipex_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_ipex_available", "kind": "Gdef"}, "is_liger_kernel_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_liger_kernel_available", "kind": "Gdef"}, "is_lomo_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_lomo_available", "kind": "Gdef"}, "is_peft_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_peft_available", "kind": "Gdef"}, "is_safetensors_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_safetensors_available", "kind": "Gdef"}, "is_sagemaker_dp_enabled": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_sagemaker_dp_enabled", "kind": "Gdef"}, "is_sagemaker_mp_enabled": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_sagemaker_mp_enabled", "kind": "Gdef"}, "is_schedulefree_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_schedulefree_available", "kind": "Gdef"}, "is_torch_greater_or_equal_than_2_3": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.is_torch_greater_or_equal_than_2_3", "kind": "Gdef"}, "is_torch_hpu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_hpu_available", "kind": "Gdef"}, "is_torch_mlu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_mlu_available", "kind": "Gdef"}, "is_torch_mps_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_mps_available", "kind": "Gdef"}, "is_torch_musa_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_musa_available", "kind": "Gdef"}, "is_torch_neuroncore_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_neuroncore_available", "kind": "Gdef"}, "is_torch_npu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_npu_available", "kind": "Gdef"}, "is_torch_xla_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_xla_available", "kind": "Gdef"}, "is_torch_xpu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_xpu_available", "kind": "Gdef"}, "is_torchao_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torchao_available", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_fsdp_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.load_fsdp_model", "name": "load_fsdp_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.load_fsdp_model", "source_any": null, "type_of_any": 3}}}, "load_fsdp_optimizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.load_fsdp_optimizer", "name": "load_fsdp_optimizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.load_fsdp_optimizer", "source_any": null, "type_of_any": 3}}}, "load_sharded_checkpoint": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.load_sharded_checkpoint", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.trainer.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "met": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.met", "name": "met", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.met", "source_any": null, "type_of_any": 3}}}, "neftune_post_forward_hook": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.neftune_post_forward_hook", "kind": "Gdef"}, "nested_concat": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.nested_concat", "kind": "Gdef"}, "nested_detach": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.nested_detach", "kind": "Gdef"}, "nested_numpify": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.nested_numpify", "kind": "Gdef"}, "nested_xla_mesh_reduce": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.nested_xla_mesh_reduce", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "number_of_arguments": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.number_of_arguments", "kind": "Gdef"}, "optuna": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.optuna", "name": "optuna", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.optuna", "source_any": null, "type_of_any": 3}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "reissue_pt_warnings": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.reissue_pt_warnings", "kind": "Gdef"}, "remove_dummy_checkpoint": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.remove_dummy_checkpoint", "kind": "Gdef"}, "requires": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.requires", "kind": "Gdef"}, "safe_globals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.trainer.safe_globals", "name": "safe_globals", "type": null}}, "safetensors": {".class": "SymbolTableNode", "cross_ref": "safetensors", "kind": "Gdef"}, "save_fsdp_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.save_fsdp_model", "name": "save_fsdp_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.save_fsdp_model", "source_any": null, "type_of_any": 3}}}, "save_fsdp_optimizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.save_fsdp_optimizer", "name": "save_fsdp_optimizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.save_fsdp_optimizer", "source_any": null, "type_of_any": 3}}}, "seed_worker": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.seed_worker", "kind": "Gdef"}, "set_rng_state_for_device": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.set_rng_state_for_device", "kind": "Gdef"}, "set_seed": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.set_seed", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "skip_first_batches": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.skip_first_batches", "name": "skip_first_batches", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.skip_first_batches", "source_any": null, "type_of_any": 3}}}, "smp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.smp", "name": "smp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.smp", "source_any": null, "type_of_any": 3}}}, "smp_forward_backward": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.smp_forward_backward", "kind": "Gdef"}, "smp_forward_only": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.smp_forward_only", "kind": "Gdef"}, "smp_gather": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.smp_gather", "kind": "Gdef"}, "smp_nested_concat": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.smp_nested_concat", "kind": "Gdef"}, "speed_metrics": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.speed_metrics", "kind": "Gdef"}, "strtobool": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.strtobool", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tpu_spmd_dataloader": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.tpu.tpu_spmd_dataloader", "kind": "Gdef"}, "unwrap_model": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.unwrap_model", "kind": "Gdef"}, "upload_folder": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.hf_api.upload_folder", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "packaging.version", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "xm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.xm", "name": "xm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.xm", "source_any": null, "type_of_any": 3}}}, "xr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.xr", "name": "xr", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.xr", "source_any": null, "type_of_any": 3}}}, "xs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.trainer.xs", "name": "xs", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.trainer.xs", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\trainer.py"}