{".class": "MypyFile", "_fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseImageProcessorFast": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_utils_fast.BaseImageProcessorFast", "kind": "Gdef", "module_public": false}, "BatchFeature": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_base.BatchFeature", "kind": "Gdef", "module_public": false}, "ChannelDimension": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.ChannelDimension", "kind": "Gdef", "module_public": false}, "DefaultFastImageProcessorKwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_utils_fast.DefaultFastImageProcessorKwargs", "kind": "Gdef", "module_public": false}, "F": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.F", "name": "F", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.vitmatte.image_processing_vitmatte_fast.F", "source_any": null, "type_of_any": 3}}}, "IMAGENET_STANDARD_MEAN": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.constants.IMAGENET_STANDARD_MEAN", "kind": "Gdef", "module_public": false}, "IMAGENET_STANDARD_STD": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.constants.IMAGENET_STANDARD_STD", "kind": "Gdef", "module_public": false}, "ImageInput": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.ImageInput", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "TensorType": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.TensorType", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.Unpack", "kind": "Gdef", "module_public": false}, "VitMatteFastImageProcessorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteFastImageProcessorKwargs", "name": "VitMatteFastImageProcessorKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteFastImageProcessorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.vitmatte.image_processing_vitmatte_fast", "mro": ["transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteFastImageProcessorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["do_resize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["default_to_square", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["resample", {".class": "UnionType", "items": ["PIL.Image.Resampling", {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_center_crop", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["crop_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_rescale", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["rescale_factor", {".class": "UnionType", "items": ["builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_normalize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_mean", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_std", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_convert_rgb", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_tensors", {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["data_format", {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_data_format", {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["device", {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_pad", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size_divisibility", "builtins.int"]], "readonly_keys": [], "required_keys": ["do_pad", "size_divisibility"]}}}, "VitMatteImageProcessorFast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.image_processing_utils_fast.BaseImageProcessorFast"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", "name": "VitMatteImageProcessorFast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.vitmatte.image_processing_vitmatte_fast", "mro": ["transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", "transformers.image_processing_utils_fast.BaseImageProcessorFast", "transformers.image_processing_utils.BaseImageProcessor", "transformers.image_processing_base.ImageProcessingMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteFastImageProcessorKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of VitMatteImageProcessorFast", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pad_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "images", "size_divisibility"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast._pad_image", "name": "_pad_image", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "images", "size_divisibility"], "arg_types": ["transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", {".class": "UnboundType", "args": [], "expr": "torch.tensor", "expr_fallback": "builtins.str", "name": "torch.tensor"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pad_image of VitMatteImageProcessorFast", "ret_type": {".class": "UnboundType", "args": [], "expr": "torch.tensor", "expr_fallback": "builtins.str", "name": "torch.tensor"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_input_trimaps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "trimaps", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast._prepare_input_trimaps", "name": "_prepare_input_trimaps", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "trimaps", "device"], "arg_types": ["transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_input_trimaps of VitMatteImageProcessorFast", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "images", "trimaps", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_pad", "size_divisibility", "return_tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast._preprocess", "name": "_preprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "images", "trimaps", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_pad", "size_divisibility", "return_tensors"], "arg_types": ["transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_preprocess of VitMatteImageProcessorFast", "ret_type": "transformers.image_processing_base.BatchFeature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast._preprocess", "name": "_preprocess", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "do_normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.do_normalize", "name": "do_normalize", "setter_type": null, "type": "builtins.bool"}}, "do_pad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.do_pad", "name": "do_pad", "setter_type": null, "type": "builtins.bool"}}, "do_rescale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.do_rescale", "name": "do_rescale", "setter_type": null, "type": "builtins.bool"}}, "image_mean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.image_mean", "name": "image_mean", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "image_std": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.image_std", "name": "image_std", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "images", "trimaps", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.preprocess", "name": "preprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "images", "trimaps", "kwargs"], "arg_types": ["transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteFastImageProcessorKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preprocess of VitMatteImageProcessorFast", "ret_type": "transformers.image_processing_base.BatchFeature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.preprocess", "name": "preprocess", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "rescale_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.rescale_factor", "name": "rescale_factor", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}}}, "size_divisibility": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.size_divisibility", "name": "size_divisibility", "setter_type": null, "type": "builtins.int"}}, "valid_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.valid_kwargs", "name": "valid_kwargs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["do_resize", "size", "default_to_square", "resample", "do_center_crop", "crop_size", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_convert_rgb", "return_tensors", "data_format", "input_data_format", "device", "do_pad", "size_divisibility"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PIL.Image.Resampling", {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypedDictType", "fallback": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteFastImageProcessorKwargs", "items": [["do_resize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["default_to_square", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["resample", {".class": "UnionType", "items": ["PIL.Image.Resampling", {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_center_crop", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["crop_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_rescale", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["rescale_factor", {".class": "UnionType", "items": ["builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_normalize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_mean", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_std", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_convert_rgb", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_tensors", {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["data_format", {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_data_format", {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["device", {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_pad", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size_divisibility", "builtins.int"]], "readonly_keys": [], "required_keys": ["do_pad", "size_divisibility"]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vitmatte.image_processing_vitmatte_fast.VitMatteImageProcessorFast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}, "filter_out_non_signature_kwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.filter_out_non_signature_kwargs", "kind": "Gdef", "module_public": false}, "get_image_size": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.get_image_size", "kind": "Gdef", "module_public": false}, "group_images_by_shape": {".class": "SymbolTableNode", "cross_ref": "transformers.image_transforms.group_images_by_shape", "kind": "Gdef", "module_public": false}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef", "module_public": false}, "is_torchvision_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torchvision_available", "kind": "Gdef", "module_public": false}, "is_torchvision_v2_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torchvision_v2_available", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vitmatte.image_processing_vitmatte_fast.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "make_list_of_images": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.make_list_of_images", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "reorder_images": {".class": "SymbolTableNode", "cross_ref": "transformers.image_transforms.reorder_images", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "validate_kwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.validate_kwargs", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vitmatte\\image_processing_vitmatte_fast.py"}