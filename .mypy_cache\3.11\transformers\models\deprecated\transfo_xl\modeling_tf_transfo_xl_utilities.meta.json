{"data_mtime": 1749061360, "dep_lines": [22, 23, 1, 1, 1, 1, 20], "dep_prios": [5, 5, 5, 30, 30, 30, 10], "dependencies": ["transformers.modeling_tf_utils", "transformers.tf_utils", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "0724aac14b775bbbbcfcd494915802d2f8f54595", "id": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities", "ignore_all": true, "interface_hash": "64f8006d5f7351545d892d06595f9ca1c70fa2f5", "mtime": 1749058944, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\deprecated\\transfo_xl\\modeling_tf_transfo_xl_utilities.py", "plugin_data": null, "size": 7633, "suppressed": ["tensorflow"], "version_id": "1.16.0"}