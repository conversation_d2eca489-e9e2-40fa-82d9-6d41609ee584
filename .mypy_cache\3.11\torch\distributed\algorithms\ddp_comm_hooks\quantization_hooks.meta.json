{"data_mtime": 1749061366, "dep_lines": [3, 4, 2, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed", "torch.nn", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.futures", "typing"], "hash": "00b4166e89ed6aaf3a29b3182883aedbe4faaf1b", "id": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks", "ignore_all": true, "interface_hash": "e1baab0d68e662759fcfdab0d2f2fa1ec0a7a7b8", "mtime": 1749057439, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\algorithms\\ddp_comm_hooks\\quantization_hooks.py", "plugin_data": null, "size": 8452, "suppressed": [], "version_id": "1.16.0"}