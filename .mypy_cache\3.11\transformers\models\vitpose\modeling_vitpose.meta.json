{"data_mtime": 1749061351, "dep_lines": [31, 21, 25, 30, 21, 22, 24, 25, 17, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 20, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.vitpose.configuration_vitpose", "torch.utils.checkpoint", "transformers.utils.logging", "transformers.utils.backbone_utils", "torch.utils", "torch.nn", "transformers.modeling_utils", "transformers.utils", "dataclasses", "typing", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._tensor", "torch.nn.init", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.batchnorm", "torch.nn.modules.conv", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.upsampling", "torch.nn.parameter", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "e7417d7b1c161704883e6e58670714443da5ea1a", "id": "transformers.models.vitpose.modeling_vitpose", "ignore_all": true, "interface_hash": "0836aa042c8b4af3c12f0c8bc5c5bfb7462f0e20", "mtime": 1749058954, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vitpose\\modeling_vitpose.py", "plugin_data": null, "size": 12852, "suppressed": [], "version_id": "1.16.0"}