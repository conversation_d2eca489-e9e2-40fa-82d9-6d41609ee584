{".class": "MypyFile", "_fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.ACT2FN", "kind": "Gdef", "module_public": false}, "FLAX_WAV2VEC2_FOR_CTC_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FLAX_WAV2VEC2_FOR_CTC_DOCSTRING", "name": "FLAX_WAV2VEC2_FOR_CTC_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "FLAX_WAV2VEC2_FOR_PRETRAINING_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FLAX_WAV2VEC2_FOR_PRETRAINING_DOCSTRING", "name": "FLAX_WAV2VEC2_FOR_PRETRAINING_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "FLAX_WAV2VEC2_MODEL_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FLAX_WAV2VEC2_MODEL_DOCSTRING", "name": "FLAX_WAV2VEC2_MODEL_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "FlaxBaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxBaseModelOutput", "kind": "Gdef", "module_public": false}, "FlaxCausalLMOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxCausalLMOutput", "kind": "Gdef", "module_public": false}, "FlaxConvLayersCollection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection", "name": "FlaxConvLayersCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvLayersCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxConvWithWeightNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm", "name": "FlaxConvWithWeightNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.__call__", "name": "__call__", "type": null}}, "_get_normed_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm._get_normed_weights", "name": "_get_normed_weights", "type": null}}, "bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.bias", "name": "bias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.conv", "name": "conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "prev_padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.prev_padding", "name": "prev_padding", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.setup", "name": "setup", "type": null}}, "weight_g": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.weight_g", "name": "weight_g", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight_v": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.weight_v", "name": "weight_v", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxConvWithWeightNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "kind": "Gdef", "module_public": false}, "FlaxWav2Vec2Adapter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter", "name": "FlaxWav2Vec2Adapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter.proj", "name": "proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "proj_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter.proj_layer_norm", "name": "proj_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Adapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2AdapterLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer", "name": "FlaxWav2Vec2AdapterLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer.conv", "name": "conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2AdapterLayersCollection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection", "name": "FlaxWav2Vec2AdapterLayersCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2AdapterLayersCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2Attention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention", "name": "FlaxWav2Vec2Attention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "attention_mask", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "attention_mask", "deterministic"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention", {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWav2Vec2Attention", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_merge_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention._merge_heads", "name": "_merge_heads", "type": null}}, "_split_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention._split_heads", "name": "_split_heads", "type": null}}, "bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.bias", "name": "bias", "setter_type": null, "type": "builtins.bool"}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dropout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.dropout", "name": "dropout", "setter_type": null, "type": "builtins.float"}}, "dropout_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.dropout_layer", "name": "dropout_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "embed_dim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.embed_dim", "name": "embed_dim", "setter_type": null, "type": "builtins.int"}}, "head_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.head_dim", "name": "head_dim", "setter_type": null, "type": "builtins.int"}}, "k_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.k_proj", "name": "k_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "num_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.num_heads", "name": "num_heads", "setter_type": null, "type": "builtins.int"}}, "out_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.out_proj", "name": "out_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "q_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.q_proj", "name": "q_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup of FlaxWav2Vec2Attention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "v_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.v_proj", "name": "v_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Attention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2BaseModelOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput", "name": "FlaxWav2Vec2BaseModelOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput.attentions", "name": "attentions", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "extract_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput.extract_features", "name": "extract_features", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput.hidden_states", "name": "hidden_states", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "last_hidden_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput.last_hidden_state", "name": "last_hidden_state", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2BaseModelOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2EncoderLayerStableLayerNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm", "name": "FlaxWav2Vec2EncoderLayerStableLayerNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "deterministic", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.__call__", "name": "__call__", "type": null}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.attention", "name": "attention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "feed_forward": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.feed_forward", "name": "feed_forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "final_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.final_layer_norm", "name": "final_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2EncoderLayerStableLayerNormCollection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection", "name": "FlaxWav2Vec2EncoderLayerStableLayerNormCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "deterministic", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "deterministic", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWav2Vec2EncoderLayerStableLayerNormCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2EncoderLayerStableLayerNormCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2FeatureEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder", "name": "FlaxWav2Vec2FeatureEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_values", "freeze_feature_encoder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "conv_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder.conv_layers", "name": "conv_layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2FeatureProjection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection", "name": "FlaxWav2Vec2FeatureProjection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection.projection", "name": "projection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeatureProjection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2FeedForward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward", "name": "FlaxWav2Vec2FeedForward", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "intermediate_act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.intermediate_act_fn", "name": "intermediate_act_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.intermediate_dense", "name": "intermediate_dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.intermediate_dropout", "name": "intermediate_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.output_dense", "name": "output_dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.output_dropout", "name": "output_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2FeedForward", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2ForCTC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTC", "name": "FlaxWav2Vec2ForCTC", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTC", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTC", "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTC.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTC.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTC", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2ForCTCModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule", "name": "FlaxWav2Vec2ForCTCModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "deterministic", "output_attentions", "output_hidden_states", "freeze_feature_encoder", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule.__call__", "name": "__call__", "type": null}}, "_get_feat_extract_output_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule._get_feat_extract_output_lengths", "name": "_get_feat_extract_output_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feat_extract_output_lengths of FlaxWav2Vec2ForCTCModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule.lm_head", "name": "lm_head", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule.setup", "name": "setup", "type": null}}, "wav2vec2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule.wav2vec2", "name": "wav2vec2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForCTCModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2ForPreTraining": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining", "name": "FlaxWav2Vec2ForPreTraining", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining", "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "gumbel_temperature", "params", "dropout_rng", "gumbel_rng", "train", "output_attentions", "output_hidden_states", "freeze_feature_encoder", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "gumbel_temperature", "params", "dropout_rng", "gumbel_rng", "train", "output_attentions", "output_hidden_states", "freeze_feature_encoder", "return_dict"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jax", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jax", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWav2Vec2ForPreTraining", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining.__call__", "name": "__call__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTraining", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2ForPreTrainingModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule", "name": "FlaxWav2Vec2ForPreTrainingModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "gumbel_temperature", "deterministic", "output_attentions", "output_hidden_states", "freeze_feature_encoder", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "gumbel_temperature", "deterministic", "output_attentions", "output_hidden_states", "freeze_feature_encoder", "return_dict"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWav2Vec2ForPreTrainingModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_feat_extract_output_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule._get_feat_extract_output_lengths", "name": "_get_feat_extract_output_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feat_extract_output_lengths of FlaxWav2Vec2ForPreTrainingModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dropout_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.dropout_features", "name": "dropout_features", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "project_hid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.project_hid", "name": "project_hid", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "project_q": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.project_q", "name": "project_q", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "quantizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.quantizer", "name": "quantizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.setup", "name": "setup", "type": null}}, "wav2vec2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.wav2vec2", "name": "wav2vec2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2ForPreTrainingOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput", "name": "FlaxWav2Vec2ForPreTrainingOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput.attentions", "name": "attentions", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "codevector_perplexity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput.codevector_perplexity", "name": "codevector_perplexity", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput.hidden_states", "name": "hidden_states", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "projected_quantized_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput.projected_quantized_states", "name": "projected_quantized_states", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "projected_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput.projected_states", "name": "projected_states", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2ForPreTrainingOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2GumbelVectorQuantizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer", "name": "FlaxWav2Vec2GumbelVectorQuantizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "mask_time_indices", "deterministic", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.__call__", "name": "__call__", "type": null}}, "_compute_perplexity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["probs", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer._compute_perplexity", "name": "_compute_perplexity", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer._compute_perplexity", "name": "_compute_perplexity", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["probs", "mask"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_perplexity of FlaxWav2Vec2GumbelVectorQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "codevectors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.codevectors", "name": "codevectors", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "num_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.num_groups", "name": "num_groups", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_vars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.num_vars", "name": "num_vars", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.setup", "name": "setup", "type": null}}, "weight_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.weight_proj", "name": "weight_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2GumbelVectorQuantizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2LayerNormConvLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer", "name": "FlaxWav2Vec2LayerNormConvLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.__call__", "name": "__call__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.conv", "name": "conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "in_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.in_conv_dim", "name": "in_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.layer_id", "name": "layer_id", "setter_type": null, "type": "builtins.int"}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_conv_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.out_conv_dim", "name": "out_conv_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2LayerNormConvLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2Model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Model", "name": "FlaxWav2Vec2Model", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Model", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Model", "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Model.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Model.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Model", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2Module": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module", "name": "FlaxWav2Vec2Module", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "deterministic", "output_attentions", "output_hidden_states", "freeze_feature_encoder", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.__call__", "name": "__call__", "type": null}}, "_get_feat_extract_output_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module._get_feat_extract_output_lengths", "name": "_get_feat_extract_output_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feat_extract_output_lengths of FlaxWav2Vec2Module", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_feature_vector_attention_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "feature_vector_length", "attention_mask", "add_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module._get_feature_vector_attention_mask", "name": "_get_feature_vector_attention_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "feature_vector_length", "attention_mask", "add_adapter"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module", "builtins.int", {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feature_vector_attention_mask of FlaxWav2Vec2Module", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adapter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.adapter", "name": "adapter", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feature_extractor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.feature_extractor", "name": "feature_extractor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feature_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.feature_projection", "name": "feature_projection", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "masked_spec_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.masked_spec_embed", "name": "masked_spec_embed", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2Module", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2PositionalConvEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding", "name": "FlaxWav2Vec2PositionalConvEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding.__call__", "name": "__call__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding.conv", "name": "conv", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "num_pad_remove": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding.num_pad_remove", "name": "num_pad_remove", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PositionalConvEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2PreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_flax_utils.FlaxPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", "name": "FlaxWav2Vec2PreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "params", "dropout_rng", "train", "output_attentions", "output_hidden_states", "freeze_feature_encoder", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_values", "attention_mask", "mask_time_indices", "params", "dropout_rng", "train", "output_attentions", "output_hidden_states", "freeze_feature_encoder", "return_dict"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jax", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWav2Vec2PreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.__call__", "name": "__call__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "kwargs"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.int", {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FlaxWav2Vec2PreTrainedModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_feat_extract_output_lengths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel._get_feat_extract_output_lengths", "name": "_get_feat_extract_output_lengths", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_lengths", "add_adapter"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}, "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_feat_extract_output_lengths of FlaxWav2Vec2PreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "hidden_size", "num_hidden_layers", "num_attention_heads", "intermediate_size", "hidden_act", "hidden_dropout", "activation_dropout", "attention_dropout", "feat_proj_dropout", "feat_quantizer_dropout", "final_dropout", "layerdrop", "initializer_range", "layer_norm_eps", "feat_extract_norm", "feat_extract_activation", "conv_dim", "conv_stride", "conv_kernel", "conv_bias", "num_conv_pos_embeddings", "num_conv_pos_embedding_groups", "do_stable_layer_norm", "apply_spec_augment", "mask_time_prob", "mask_time_length", "mask_time_min_masks", "mask_feature_prob", "mask_feature_length", "mask_feature_min_masks", "num_codevectors_per_group", "num_codevector_groups", "contrastive_logits_temperature", "num_negatives", "codevector_dim", "proj_codevector_dim", "diversity_loss_weight", "ctc_loss_reduction", "ctc_zero_infinity", "use_weighted_layer_sum", "classifier_proj_size", "tdnn_dim", "tdnn_kernel", "tdnn_dilation", "xvector_output_dim", "pad_token_id", "bos_token_id", "eos_token_id", "add_adapter", "adapter_kernel_size", "adapter_stride", "num_adapter_layers", "output_hidden_size", "adapter_attn_dim", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.init_weights", "name": "init_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "arg_types": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jax", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FrozenDict", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_weights of FlaxWav2Vec2PreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FrozenDict", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.main_input_name", "name": "main_input_name", "setter_type": null, "type": "builtins.str"}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.module_class", "name": "module_class", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2PreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWav2Vec2StableLayerNormEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder", "name": "FlaxWav2Vec2StableLayerNormEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2", "mro": ["transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "deterministic", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.__call__", "name": "__call__", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.config", "name": "config", "setter_type": null, "type": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos_conv_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.pos_conv_embed", "name": "pos_conv_embed", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FlaxWav2Vec2StableLayerNormEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FrozenDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FrozenDict", "name": "FrozenDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.FrozenDict", "source_any": null, "type_of_any": 3}}}, "ModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ModelOutput", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WAV2VEC2_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.WAV2VEC2_INPUTS_DOCSTRING", "name": "WAV2VEC2_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "WAV2VEC2_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.WAV2VEC2_START_DOCSTRING", "name": "WAV2VEC2_START_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "Wav2Vec2Config": {".class": "SymbolTableNode", "cross_ref": "transformers.models.wav2vec2.configuration_wav2vec2.Wav2Vec2Config", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_compute_mask_indices": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["shape", "mask_prob", "mask_length", "attention_mask", "min_masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2._compute_mask_indices", "name": "_compute_mask_indices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["shape", "mask_prob", "mask_length", "attention_mask", "min_masks"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_mask_indices", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sample_negative_indices": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["features_shape", "num_negatives", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2._sample_negative_indices", "name": "_sample_negative_indices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["features_shape", "num_negatives", "attention_mask"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sample_negative_indices", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "append_replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.append_replace_return_docstrings", "kind": "Gdef", "module_public": false}, "dot_product_attention_weights": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.dot_product_attention_weights", "name": "dot_product_attention_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.dot_product_attention_weights", "source_any": null, "type_of_any": 3}}}, "flatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.flatten_dict", "name": "flatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.flatten_dict", "source_any": null, "type_of_any": 3}}}, "flax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.flax", "name": "flax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.flax", "source_any": null, "type_of_any": 3}}}, "freeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.freeze", "name": "freeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.freeze", "source_any": null, "type_of_any": 3}}}, "jax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jax", "name": "jax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jax", "source_any": null, "type_of_any": 3}}}, "jnp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "name": "jnp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.jnp", "source_any": null, "type_of_any": 3}}}, "lax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.lax", "name": "lax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.lax", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "name": "nn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.nn", "source_any": null, "type_of_any": 3}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "overwrite_call_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.overwrite_call_docstring", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "unflatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.unflatten_dict", "name": "unflatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.unflatten_dict", "source_any": null, "type_of_any": 3}}}, "unfreeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.wav2vec2.modeling_flax_wav2vec2.unfreeze", "name": "unfreeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.wav2vec2.modeling_flax_wav2vec2.unfreeze", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\wav2vec2\\modeling_flax_wav2vec2.py"}