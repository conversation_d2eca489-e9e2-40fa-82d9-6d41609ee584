{"data_mtime": 1749061343, "dep_lines": [24, 23, 19, 21, 22, 23, 17, 19, 28, 157, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 20, 5, 20, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.utils.logging", "packaging.version", "transformers.configuration_utils", "transformers.onnx", "transformers.utils", "typing", "packaging", "transformers", "torch", "builtins", "_frozen_importlib", "abc", "enum", "logging", "transformers.models.auto", "transformers.onnx.config", "transformers.tokenization_utils_base", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "4de89d45fcebb01ab2ccabc68fbc3e3219252a6b", "id": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder", "ignore_all": true, "interface_hash": "c1b9e5cddac170fee61ed2fb532a3770719428eb", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vision_encoder_decoder\\configuration_vision_encoder_decoder.py", "plugin_data": null, "size": 8425, "suppressed": [], "version_id": "1.16.0"}