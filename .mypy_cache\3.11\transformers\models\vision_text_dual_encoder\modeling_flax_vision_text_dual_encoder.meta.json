{"data_mtime": 1749061346, "dep_lines": [27, 28, 29, 30, 26, 25, 26, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22, 19, 21, 23, 19, 20], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10, 5, 20, 10], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.auto.modeling_flax_auto", "transformers.models.clip.modeling_flax_clip", "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder", "transformers.utils.logging", "transformers.modeling_flax_utils", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc", "collections", "logging", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.models.clip", "transformers.models.clip.configuration_clip", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub"], "hash": "e275c7d391a14a9b8f14b07a1a8e7a8475c773a6", "id": "transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder", "ignore_all": true, "interface_hash": "75e0751c14100ecd2cc7084941aad354b9bca5c5", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\modeling_flax_vision_text_dual_encoder.py", "plugin_data": null, "size": 26411, "suppressed": ["flax.core.frozen_dict", "flax.linen", "jax.numpy", "flax.traverse_util", "flax", "jax"], "version_id": "1.16.0"}