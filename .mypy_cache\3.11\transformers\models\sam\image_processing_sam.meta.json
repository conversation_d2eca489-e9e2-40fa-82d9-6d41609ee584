{"data_mtime": 1749061348, "dep_lines": [40, 53, 24, 25, 26, 40, 53, 62, 17, 18, 19, 20, 22, 52, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 56, 60, 59], "dep_prios": [10, 10, 5, 5, 5, 5, 20, 5, 10, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10], "dependencies": ["transformers.utils.logging", "torch.nn.functional", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "torch.nn", "transformers.tf_utils", "math", "copy", "itertools", "typing", "numpy", "torch", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "_typeshed", "abc", "enum", "logging", "torch._C", "torch._tensor", "transformers.image_processing_base", "transformers.utils.constants", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "99345f0c986c93b8af58af9418b0ea155938041b", "id": "transformers.models.sam.image_processing_sam", "ignore_all": true, "interface_hash": "6edecacefe18e720e136a7325ac72869af251543", "mtime": 1749058953, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\sam\\image_processing_sam.py", "plugin_data": null, "size": 67734, "suppressed": ["torchvision.ops.boxes", "tensorflow.experimental", "tensorflow"], "version_id": "1.16.0"}