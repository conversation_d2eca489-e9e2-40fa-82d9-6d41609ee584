{"data_mtime": 1749061360, "dep_lines": [25, 28, 23, 27, 26, 27, 15, 16, 17, 18, 19, 21, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 10, 10, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.data.processors.squad", "torch.utils.data", "transformers.utils.logging", "transformers.tokenization_utils", "transformers.utils", "os", "time", "dataclasses", "enum", "typing", "torch", "filelock", "builtins", "_frozen_importlib", "abc", "collections", "contextlib", "filelock._api", "filelock._soft", "genericpath", "logging", "ntpath", "torch._C", "torch._tensor", "torch.serialization", "torch.types", "torch.utils", "torch.utils.data.dataset", "transformers.data.processors", "transformers.data.processors.utils", "transformers.models", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.tokenization_utils_base", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "5f0285cab77aa97349b6f656ec3c43788fd6e720", "id": "transformers.data.datasets.squad", "ignore_all": true, "interface_hash": "ecbd89fb72fecae2040ead514cebe863acbdb68a", "mtime": 1749058937, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\data\\datasets\\squad.py", "plugin_data": null, "size": 9307, "suppressed": [], "version_id": "1.16.0"}