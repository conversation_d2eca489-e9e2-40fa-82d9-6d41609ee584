{".class": "MypyFile", "_fullname": "transformers.models.swiftformer.modeling_swiftformer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2CLS": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2CLS", "kind": "Gdef", "module_public": false}, "BCEWithLogitsLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.BCEWithLogitsLoss", "kind": "Gdef", "module_public": false}, "BaseModelOutputWithNoAttention": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutputWithNoAttention", "kind": "Gdef", "module_public": false}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.CrossEntropyLoss", "kind": "Gdef", "module_public": false}, "ImageClassifierOutputWithNoAttention": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.ImageClassifierOutputWithNoAttention", "kind": "Gdef", "module_public": false}, "MSELoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.MSELoss", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "SwiftFormerConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "kind": "Gdef", "module_public": false}, "SwiftFormerConvEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder", "name": "SwiftFormerConvEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "dim"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerConvEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.act", "name": "act", "setter_type": null, "type": "torch.nn.modules.activation.GELU"}}, "depth_wise_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.depth_wise_conv", "name": "depth_wise_conv", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.drop_path", "name": "drop_path", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.forward", "name": "forward", "type": null}}, "layer_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.layer_scale", "name": "layer_scale", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.norm", "name": "norm", "setter_type": null, "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "point_wise_conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.point_wise_conv1", "name": "point_wise_conv1", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}, "point_wise_conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.point_wise_conv2", "name": "point_wise_conv2", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerConvEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerDropPath": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath", "name": "SwiftFormerDropPath", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerDropPath", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop_prob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath.drop_prob", "name": "drop_prob", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "extra_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath.extra_repr", "name": "extra_repr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_repr of SwiftFormerDropPath", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of SwiftFormerDropPath", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerEfficientAdditiveAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention", "name": "SwiftFormerEfficientAdditiveAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "dim"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerEfficientAdditiveAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "final": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.final", "name": "final", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.forward", "name": "forward", "type": null}}, "proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.proj", "name": "proj", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "scale_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.scale_factor", "name": "scale_factor", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "to_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.to_key", "name": "to_key", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "to_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.to_query", "name": "to_query", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}, "w_g": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.w_g", "name": "w_g", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings", "name": "Swift<PERSON>ormerEmbe<PERSON>s", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "index"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings.forward", "name": "forward", "type": null}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings.norm", "name": "norm", "setter_type": null, "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings.proj", "name": "proj", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder", "name": "Swift<PERSON>orm<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder.config", "name": "config", "setter_type": null, "type": "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder", "torch._tensor.Tensor", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of <PERSON><PERSON>orm<PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.BaseModelOutputWithNoAttention"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "network": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder.network", "name": "network", "setter_type": null, "type": "torch.nn.modules.container.ModuleList"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerEncoderBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock", "name": "SwiftFormerEncoderBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "config", "dim", "drop_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "config", "dim", "drop_path"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerEncoderBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.attn", "name": "attn", "setter_type": null, "type": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEfficientAdditiveAttention"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.drop_path", "name": "drop_path", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerDropPath", "torch.nn.modules.linear.Identity"], "uses_pep604_syntax": false}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.forward", "name": "forward", "type": null}}, "layer_scale_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.layer_scale_1", "name": "layer_scale_1", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "layer_scale_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.layer_scale_2", "name": "layer_scale_2", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "linear": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.linear", "name": "linear", "setter_type": null, "type": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp"}}, "local_representation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.local_representation", "name": "local_representation", "setter_type": null, "type": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation"}}, "use_layer_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.use_layer_scale", "name": "use_layer_scale", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoderBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerForImageClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification", "name": "SwiftFormerForImageClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification", "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerForImageClassification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dist_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.dist_head", "name": "dist_head", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.nn.modules.linear.Linear", "torch.nn.modules.linear.Identity"], "uses_pep604_syntax": false}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of SwiftFormerForImageClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.ImageClassifierOutputWithNoAttention"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.head", "name": "head", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.nn.modules.linear.Linear", "torch.nn.modules.linear.Identity"], "uses_pep604_syntax": false}}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.norm", "name": "norm", "setter_type": null, "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.num_labels", "name": "num_labels", "setter_type": null, "type": "builtins.int"}}, "swiftformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.swiftformer", "name": "swiftformer", "setter_type": null, "type": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerForImageClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerLocalRepresentation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation", "name": "SwiftFormerLocalRepresentation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "dim"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerLocalRepresentation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.act", "name": "act", "setter_type": null, "type": "torch.nn.modules.activation.GELU"}}, "depth_wise_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.depth_wise_conv", "name": "depth_wise_conv", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.drop_path", "name": "drop_path", "setter_type": null, "type": "torch.nn.modules.linear.Identity"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.forward", "name": "forward", "type": null}}, "layer_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.layer_scale", "name": "layer_scale", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.norm", "name": "norm", "setter_type": null, "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "point_wise_conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.point_wise_conv1", "name": "point_wise_conv1", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}, "point_wise_conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.point_wise_conv2", "name": "point_wise_conv2", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerLocalRepresentation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerMlp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp", "name": "SwiftFormerMlp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "in_features"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "in_features"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerMlp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp.act", "name": "act", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "drop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp.drop", "name": "drop", "setter_type": null, "type": "torch.nn.modules.dropout.Dropout"}}, "fc1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp.fc1", "name": "fc1", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}, "fc2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp.fc2", "name": "fc2", "setter_type": null, "type": "torch.nn.modules.conv.Conv2d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp.forward", "name": "forward", "type": null}}, "norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp.norm1", "name": "norm1", "setter_type": null, "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerMlp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel", "name": "SwiftFormerModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel", "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel.encoder", "name": "encoder", "setter_type": null, "type": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerEncoder"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of SwiftFormerModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.BaseModelOutputWithNoAttention"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "patch_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel.patch_embed", "name": "patch_embed", "setter_type": null, "type": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerPatchEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding", "name": "SwiftFormerPatchEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerPatchEmbedding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding.forward", "name": "forward", "type": null}}, "patch_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding.patch_embedding", "name": "patch_embedding", "setter_type": null, "type": "torch.nn.modules.container.Sequential"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPatchEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel", "name": "SwiftFormerPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel._init_weights", "name": "_init_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel", {".class": "UnionType", "items": ["torch.nn.modules.linear.Linear", "torch.nn.modules.conv.Conv2d", "torch.nn.modules.normalization.LayerNorm"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_weights of SwiftFormerPreTrainedModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_no_split_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel._no_split_modules", "name": "_no_split_modules", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["image_size", "num_channels", "depths", "embed_dims", "mlp_ratio", "downsamples", "hidden_act", "down_patch_size", "down_stride", "down_pad", "drop_path_rate", "drop_mlp_rate", "drop_conv_encoder_rate", "use_layer_scale", "layer_scale_init_value", "batch_norm_eps", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel.main_input_name", "name": "main_input_name", "setter_type": null, "type": "builtins.str"}}, "supports_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel.supports_gradient_checkpointing", "name": "supports_gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwiftFormerStage": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage", "name": "SwiftFormerStage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_swiftformer", "mro": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "index"], "arg_types": ["transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SwiftFormerStage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage.blocks", "name": "blocks", "setter_type": null, "type": "torch.nn.modules.container.ModuleList"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_swiftformer.SwiftFormerStage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "drop_path": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["input", "drop_prob", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_swiftformer.drop_path", "name": "drop_path", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["input", "drop_prob", "training"], "arg_types": ["torch._tensor.Tensor", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_path", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_swiftformer.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\swiftformer\\modeling_swiftformer.py"}