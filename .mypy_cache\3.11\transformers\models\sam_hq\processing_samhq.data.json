{".class": "MypyFile", "_fullname": "transformers.models.sam_hq.processing_samhq", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AudioInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.AudioInput", "kind": "Gdef", "module_public": false}, "BatchEncoding": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.BatchEncoding", "kind": "Gdef", "module_public": false}, "ImageInput": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.ImageInput", "kind": "Gdef", "module_public": false}, "ImagesKwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ImagesKwargs", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTokenizedInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTokenizedInput", "kind": "Gdef", "module_public": false}, "ProcessingKwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ProcessingKwargs", "kind": "Gdef", "module_public": false}, "ProcessorMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ProcessorMixin", "kind": "Gdef", "module_public": false}, "SamHQImagesKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam_hq.processing_samhq.SamHQImagesKwargs", "name": "SamHQImagesKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQImagesKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam_hq.processing_samhq", "mro": ["transformers.models.sam_hq.processing_samhq.SamHQImagesKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["do_resize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size_divisor", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["crop_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["resample", {".class": "UnionType", "items": ["PIL.Image.Resampling", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_rescale", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["rescale_factor", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_normalize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_mean", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_std", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_pad", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["pad_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_center_crop", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["data_format", {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_data_format", {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["device", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["segmentation_maps", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_points", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_labels", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_boxes", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["point_pad_value", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": ["input_boxes", "input_labels", "input_points", "point_pad_value", "segmentation_maps"]}}}, "SamHQProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.processing_utils.ProcessorMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor", "name": "SamHQProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam_hq.processing_samhq", "mro": ["transformers.models.sam_hq.processing_samhq.SamHQProcessor", "transformers.processing_utils.ProcessorMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 5, 5, 5, 4], "arg_names": ["self", "images", "args", "text", "audio", "video", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 5, 5, 5, 4], "arg_names": ["self", "images", "args", "text", "audio", "video", "kwargs"], "arg_types": ["transformers.models.sam_hq.processing_samhq.SamHQProcessor", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.AudioInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.video_utils.VideoInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.sam_hq.processing_samhq.SamHQProcessorKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of SamHQProcessor", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "image_processor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.__init__", "name": "__init__", "type": null}}, "_check_and_preprocess_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "input_points", "input_labels", "input_boxes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor._check_and_preprocess_points", "name": "_check_and_preprocess_points", "type": null}}, "_normalize_and_convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "encoding_image_processor", "original_sizes", "input_points", "input_labels", "input_boxes", "return_tensors", "point_pad_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor._normalize_and_convert", "name": "_normalize_and_convert", "type": null}}, "_normalize_batch_coordinates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "inputs", "original_sizes", "is_bounding_box"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor._normalize_batch_coordinates", "name": "_normalize_batch_coordinates", "type": null}}, "_normalize_coordinates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "target_size", "coords", "original_size", "is_bounding_box"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor._normalize_coordinates", "name": "_normalize_coordinates", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "target_size", "coords", "original_size", "is_bounding_box"], "arg_types": ["transformers.models.sam_hq.processing_samhq.SamHQProcessor", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_coordinates of SamHQProcessor", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pad_points_and_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input_points", "input_labels", "point_pad_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor._pad_points_and_labels", "name": "_pad_points_and_labels", "type": null}}, "_preprocess_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "inputs", "error_message", "expected_nesting", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor._preprocess_input", "name": "_preprocess_input", "type": null}}, "_to_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "array", "min_dim", "return_tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor._to_tensor", "name": "_to_tensor", "type": null}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.attributes", "name": "attributes", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "image_processor_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.image_processor_class", "name": "image_processor_class", "setter_type": null, "type": "builtins.str"}}, "model_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.model_input_names", "name": "model_input_names", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.model_input_names", "name": "model_input_names", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.sam_hq.processing_samhq.SamHQProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_input_names of SamHQProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "optional_call_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.optional_call_args", "name": "optional_call_args", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "post_process_masks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.post_process_masks", "name": "post_process_masks", "type": null}}, "target_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.target_size", "name": "target_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam_hq.processing_samhq.SamHQProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SamHQProcessorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessorKwargs", "name": "SamHQProcessorKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.sam_hq.processing_samhq.SamHQProcessorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam_hq.processing_samhq", "mro": ["transformers.models.sam_hq.processing_samhq.SamHQProcessorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["return_tensors", {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["sampling_rate", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["raw_speech", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["padding", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.utils.generic.PaddingStrategy"], "uses_pep604_syntax": false}], ["max_length", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["truncation", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", "transformers.tokenization_utils_base.TruncationStrategy"], "uses_pep604_syntax": false}], ["pad_to_multiple_of", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_attention_mask", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_convert_rgb", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_resize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size_divisor", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["default_to_square", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["resample", {".class": "UnionType", "items": ["PIL.Image.Resampling", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_rescale", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["rescale_factor", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_normalize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_mean", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_std", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_pad", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_center_crop", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["crop_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["data_format", {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_data_format", {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["device", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["pad_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["text_pair", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["text_target", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], ["text_pair_target", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["add_special_tokens", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["stride", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["is_split_into_words", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_token_type_ids", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_overflowing_tokens", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_special_tokens_mask", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_offsets_mapping", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_length", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["verbose", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["padding_side", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["common_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.processing_utils.CommonKwargs"}], ["text_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.processing_utils.TextKwargs"}], ["images_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.sam_hq.processing_samhq.SamHQImagesKwargs"}], ["videos_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.processing_utils.VideosKwargs"}], ["audio_kwargs", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.processing_utils.AudioKwargs"}], ["_defaults", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}]], "readonly_keys": [], "required_keys": []}}}, "TextInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.TextInput", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.Unpack", "kind": "Gdef", "module_public": false}, "VideoInput": {".class": "SymbolTableNode", "cross_ref": "transformers.video_utils.VideoInput", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam_hq.processing_samhq.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam_hq.processing_samhq.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam_hq.processing_samhq.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam_hq.processing_samhq.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam_hq.processing_samhq.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam_hq.processing_samhq.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam_hq.processing_samhq.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef", "module_public": false}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\sam_hq\\processing_samhq.py"}