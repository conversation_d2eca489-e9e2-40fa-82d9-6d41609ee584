{"data_mtime": 1749061347, "dep_lines": [25, 25, 34, 17, 18, 19, 20, 22, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23, 32], "dep_prios": [10, 5, 5, 10, 10, 5, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 25], "dependencies": ["transformers.utils.logging", "transformers.utils", "transformers.tokenization_utils", "json", "os", "functools", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "logging", "torch._C", "torch._tensor", "transformers.tokenization_utils_base", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "dcfcdd9158122713096962a6afa4a0c64dd5baf8", "id": "transformers.models.codegen.tokenization_codegen", "ignore_all": true, "interface_hash": "edd2fc42f334cceb849484e867b299d50886ddc1", "mtime": 1749058941, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\codegen\\tokenization_codegen.py", "plugin_data": null, "size": 16573, "suppressed": ["regex", "tensorflow"], "version_id": "1.16.0"}