{"data_mtime": 1749061348, "dep_lines": [37, 32, 33, 22, 23, 24, 25, 26, 27, 19, 20, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 49], "dep_prios": [25, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.models.omdet_turbo.modeling_omdet_turbo", "transformers.utils.deprecation", "transformers.utils.import_utils", "transformers.feature_extraction_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.utils", "warnings", "typing", "torch", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "abc", "collections", "enum", "numpy", "torch._C", "torch._tensor", "transformers.utils.generic", "transformers.utils.hub"], "hash": "562ef1fe8a38a4e23ef3571071e78fa444e25306", "id": "transformers.models.omdet_turbo.processing_omdet_turbo", "ignore_all": true, "interface_hash": "a654036bb58ca76a6ea84b46a55a3be300b79960", "mtime": 1749058950, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\omdet_turbo\\processing_omdet_turbo.py", "plugin_data": null, "size": 17538, "suppressed": ["torchvision.ops.boxes"], "version_id": "1.16.0"}