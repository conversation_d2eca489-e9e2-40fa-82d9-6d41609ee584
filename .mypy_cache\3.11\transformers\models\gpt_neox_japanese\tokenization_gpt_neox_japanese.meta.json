{"data_mtime": 1749061347, "dep_lines": [27, 26, 27, 17, 18, 19, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.tokenization_utils_fast", "transformers.utils", "collections", "json", "os", "re", "sys", "typing", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "genericpath", "logging", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence", "numpy.core", "numpy.core.fromnumeric", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils.hub", "typing_extensions"], "hash": "302bc153f820080a723ee33be246420b01b990b8", "id": "transformers.models.gpt_neox_japanese.tokenization_gpt_neox_japanese", "ignore_all": true, "interface_hash": "3c76a6eeb97ec6993d1dfdea43626d7c150fb4cb", "mtime": 1749058946, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\gpt_neox_japanese\\tokenization_gpt_neox_japanese.py", "plugin_data": null, "size": 16958, "suppressed": [], "version_id": "1.16.0"}