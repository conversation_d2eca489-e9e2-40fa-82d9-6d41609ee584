{"data_mtime": 1749061347, "dep_lines": [27, 26, 27, 18, 19, 20, 21, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24, 122], "dep_prios": [10, 5, 20, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20], "dependencies": ["transformers.utils.logging", "transformers.tokenization_utils", "transformers.utils", "html", "os", "re", "shutil", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "logging", "transformers.tokenization_utils_base", "transformers.utils.hub", "typing_extensions"], "hash": "b858f2f6a2780545970a704f9a6ae945c7c61698", "id": "transformers.models.bertweet.tokenization_bertweet", "ignore_all": true, "interface_hash": "a59b8f7a0207bfd8ddc3adf14a2fdb75bce2c5a3", "mtime": 1749058940, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\bertweet\\tokenization_bertweet.py", "plugin_data": null, "size": 27020, "suppressed": ["regex", "emoji"], "version_id": "1.16.0"}