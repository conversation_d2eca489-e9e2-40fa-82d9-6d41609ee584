{"data_mtime": 1749061347, "dep_lines": [35, 24, 25, 26, 35, 17, 18, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22], "dep_prios": [10, 5, 5, 5, 20, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.utils.logging", "transformers.file_utils", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils", "json", "os", "functools", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "collections", "enum", "io", "json.decoder", "logging", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "756640fd838f835ba70ec95c6b005d614d26d0cf", "id": "transformers.models.markuplm.tokenization_markuplm", "ignore_all": true, "interface_hash": "07d15221ea92a2a553e7ac1b67bd4e7afb354fcf", "mtime": 1749058949, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\markuplm\\tokenization_markuplm.py", "plugin_data": null, "size": 70173, "suppressed": ["regex"], "version_id": "1.16.0"}