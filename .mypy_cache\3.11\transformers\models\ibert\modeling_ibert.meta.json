{"data_mtime": 1749061356, "dep_lines": [41, 42, 24, 40, 24, 25, 28, 29, 38, 39, 40, 20, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 20, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.ibert.configuration_ibert", "transformers.models.ibert.quant_modules", "torch.utils.checkpoint", "transformers.utils.logging", "torch.utils", "torch.nn", "transformers.activations", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.pytorch_utils", "transformers.utils", "math", "typing", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.container", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.parameter", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "4e79bf4d19bb8ff20bd2d92df874c1d6a629a5da", "id": "transformers.models.ibert.modeling_ibert", "ignore_all": true, "interface_hash": "9021ea650e10401a25972ab1f909692611fc2662", "mtime": 1749058946, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\ibert\\modeling_ibert.py", "plugin_data": null, "size": 51669, "suppressed": [], "version_id": "1.16.0"}