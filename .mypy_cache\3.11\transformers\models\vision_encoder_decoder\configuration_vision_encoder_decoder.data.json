{".class": "MypyFile", "_fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AutoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.configuration_auto.AutoConfig", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "OnnxConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.onnx.config.OnnxConfig", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "typing.OrderedDict", "kind": "Gdef", "module_public": false}, "PreTrainedTokenizerBase": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "kind": "Gdef", "module_public": false}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TensorType": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.TensorType", "kind": "Gdef", "module_public": false}, "VisionEncoderDecoderConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.configuration_utils.PretrainedConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig", "name": "VisionEncoderDecoderConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder", "mro": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.__init__", "name": "__init__", "type": null}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.decoder", "name": "decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "from_encoder_decoder_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "encoder_config", "decoder_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.from_encoder_decoder_configs", "name": "from_encoder_decoder_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "encoder_config", "decoder_config", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig"}, "transformers.configuration_utils.PretrainedConfig", "transformers.configuration_utils.PretrainedConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_encoder_decoder_configs of VisionEncoderDecoderConfig", "ret_type": "transformers.configuration_utils.PretrainedConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.from_encoder_decoder_configs", "name": "from_encoder_decoder_configs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "encoder_config", "decoder_config", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig"}, "transformers.configuration_utils.PretrainedConfig", "transformers.configuration_utils.PretrainedConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_encoder_decoder_configs of VisionEncoderDecoderConfig", "ret_type": "transformers.configuration_utils.PretrainedConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_no_defaults_at_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.has_no_defaults_at_init", "name": "has_no_defaults_at_init", "setter_type": null, "type": "builtins.bool"}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.model_type", "name": "model_type", "setter_type": null, "type": "builtins.str"}}, "sub_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.sub_configs", "name": "sub_configs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "transformers.configuration_utils.PretrainedConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VisionEncoderDecoderDecoderOnnxConfig": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.onnx.config.OnnxConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig", "name": "VisionEncoderDecoderDecoderOnnxConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder", "mro": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig", "transformers.onnx.config.OnnxConfig", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "generate_dummy_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "tokenizer", "batch_size", "seq_length", "is_pair", "framework"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig.generate_dummy_inputs", "name": "generate_dummy_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "tokenizer", "batch_size", "seq_length", "is_pair", "framework"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig", "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_dummy_inputs of VisionEncoderDecoderDecoderOnnxConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig.inputs", "name": "inputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inputs of VisionEncoderDecoderDecoderOnnxConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig.inputs", "name": "inputs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inputs of VisionEncoderDecoderDecoderOnnxConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderDecoderOnnxConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VisionEncoderDecoderEncoderOnnxConfig": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.onnx.config.OnnxConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig", "name": "VisionEncoderDecoderEncoderOnnxConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder", "mro": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig", "transformers.onnx.config.OnnxConfig", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "atol_for_validation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig.atol_for_validation", "name": "atol_for_validation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atol_for_validation of VisionEncoderDecoderEncoderOnnxConfig", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig.atol_for_validation", "name": "atol_for_validation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "atol_for_validation of VisionEncoderDecoderEncoderOnnxConfig", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig.inputs", "name": "inputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inputs of VisionEncoderDecoderEncoderOnnxConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig.inputs", "name": "inputs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inputs of VisionEncoderDecoderEncoderOnnxConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig.outputs", "name": "outputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outputs of VisionEncoderDecoderEncoderOnnxConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig.outputs", "name": "outputs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outputs of VisionEncoderDecoderEncoderOnnxConfig", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "torch_onnx_minimum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig.torch_onnx_minimum_version", "name": "torch_onnx_minimum_version", "setter_type": null, "type": "packaging.version.Version"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderEncoderOnnxConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VisionEncoderDecoderOnnxConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.onnx.config.OnnxConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig", "name": "VisionEncoderDecoderOnnxConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder", "mro": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig", "transformers.onnx.config.OnnxConfig", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_decoder_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "encoder_config", "decoder_config", "feature"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig.get_decoder_config", "name": "get_decoder_config", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "encoder_config", "decoder_config", "feature"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig", "transformers.configuration_utils.PretrainedConfig", "transformers.configuration_utils.PretrainedConfig", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_decoder_config of VisionEncoderDecoderOnnxConfig", "ret_type": "transformers.onnx.config.OnnxConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_encoder_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoder_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig.get_encoder_config", "name": "get_encoder_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "encoder_config"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig", "transformers.configuration_utils.PretrainedConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_encoder_config of VisionEncoderDecoderOnnxConfig", "ret_type": "transformers.onnx.config.OnnxConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig.inputs", "name": "inputs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inputs of VisionEncoderDecoderOnnxConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig.inputs", "name": "inputs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inputs of VisionEncoderDecoderOnnxConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.VisionEncoderDecoderOnnxConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "version": {".class": "SymbolTableNode", "cross_ref": "packaging.version", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\transformers\\models\\vision_encoder_decoder\\configuration_vision_encoder_decoder.py"}