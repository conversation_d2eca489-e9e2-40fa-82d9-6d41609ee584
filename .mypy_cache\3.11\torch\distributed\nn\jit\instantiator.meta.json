{"data_mtime": 1749061365, "dep_lines": [12, 3, 4, 5, 6, 7, 8, 9, 11, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30], "dependencies": ["torch.distributed.nn.jit.templates.remote_module_template", "atexit", "importlib", "logging", "os", "sys", "tempfile", "typing", "torch", "builtins", "_frozen_importlib", "abc", "types"], "hash": "5668b78ac890e1ea9b04fe44d7758ae3ec61c8d7", "id": "torch.distributed.nn.jit.instantiator", "ignore_all": true, "interface_hash": "fa14dc382705b0a4d720bd244ce6dd52a5395930", "mtime": 1749057438, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Qwen\\venv\\Lib\\site-packages\\torch\\distributed\\nn\\jit\\instantiator.py", "plugin_data": null, "size": 5666, "suppressed": [], "version_id": "1.16.0"}